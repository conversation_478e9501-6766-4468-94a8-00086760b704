"""
Market data ingestion module for Indian stocks
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import requests
from bs4 import BeautifulSoup
import time
import logging
from dataclasses import dataclass

from .models import StockData, MarketData, TechnicalIndicators

logger = logging.getLogger(__name__)


@dataclass
class MarketDataSource:
    """Market data source configuration"""
    name: str
    base_url: str
    api_key: Optional[str] = None
    rate_limit: int = 1  # requests per second


class MarketDataProvider:
    """Main market data provider for Indian stocks"""
    
    def __init__(self):
        self.sources = {
            'yfinance': MarketDataSource('Yahoo Finance', 'https://finance.yahoo.com'),
            'nse': MarketDataSource('NSE', 'https://www.nseindia.com'),
            'bse': MarketDataSource('BSE', 'https://www.bseindia.com')
        }
        self.cache = {}
        self.cache_ttl = 60  # seconds
        
    def get_stock_data(self, symbol: str, period: str = "1d", interval: str = "1m") -> pd.DataFrame:
        """
        Get stock data from Yahoo Finance
        
        Args:
            symbol: Stock symbol (e.g., 'TCS.NS', 'RELIANCE.NS')
            period: Data period ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
            interval: Data interval ('1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo')
        
        Returns:
            DataFrame with OHLCV data
        """
        try:
            # Add .NS suffix if not present for Indian stocks
            if not symbol.endswith('.NS') and not symbol.endswith('.BO'):
                symbol = f"{symbol}.NS"
            
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                logger.warning(f"No data received for {symbol}")
                return pd.DataFrame()
            
            # Reset index to make timestamp a column
            data = data.reset_index()
            
            # Rename columns to match our model
            data.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            
            # Calculate VWAP
            data['vwap'] = (data['volume'] * (data['high'] + data['low'] + data['close']) / 3).cumsum() / data['volume'].cumsum()
            
            return data
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    def get_fundamental_data(self, symbol: str) -> Dict:
        """
        Get fundamental data for a stock
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with fundamental metrics
        """
        try:
            if not symbol.endswith('.NS') and not symbol.endswith('.BO'):
                symbol = f"{symbol}.NS"
            
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            fundamental_data = {
                'symbol': symbol,
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'pb_ratio': info.get('priceToBook'),
                'debt_to_equity': info.get('debtToEquity'),
                'roe': info.get('returnOnEquity'),
                'roic': info.get('returnOnInvestedCapital'),
                'revenue_growth': info.get('revenueGrowth'),
                'profit_growth': info.get('earningsGrowth'),
                'dividend_yield': info.get('dividendYield'),
                'beta': info.get('beta'),
                'sector': info.get('sector'),
                'industry': info.get('industry')
            }
            
            return fundamental_data
            
        except Exception as e:
            logger.error(f"Error fetching fundamental data for {symbol}: {str(e)}")
            return {}
    
    def get_market_breadth(self) -> Dict:
        """
        Get market breadth data (advance/decline ratio)
        
        Returns:
            Dictionary with market breadth metrics
        """
        try:
            # Get Nifty 50 components
            nifty50_symbols = [
                'RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'ICICIBANK.NS',
                'HINDUNILVR.NS', 'ITC.NS', 'SBIN.NS', 'BHARTIARTL.NS', 'AXISBANK.NS'
            ]
            
            advances = 0
            declines = 0
            
            for symbol in nifty50_symbols:
                try:
                    data = self.get_stock_data(symbol, period="2d", interval="1d")
                    if not data.empty and len(data) >= 2:
                        current_price = data.iloc[-1]['close']
                        prev_price = data.iloc[-2]['close']
                        
                        if current_price > prev_price:
                            advances += 1
                        elif current_price < prev_price:
                            declines += 1
                            
                except Exception as e:
                    logger.warning(f"Error processing {symbol}: {str(e)}")
                    continue
            
            total = advances + declines
            advance_decline_ratio = advances / declines if declines > 0 else float('inf')
            
            return {
                'advances': advances,
                'declines': declines,
                'advance_decline_ratio': advance_decline_ratio,
                'total_stocks': total
            }
            
        except Exception as e:
            logger.error(f"Error calculating market breadth: {str(e)}")
            return {}
    
    def get_sector_performance(self) -> Dict[str, float]:
        """
        Get sector-wise performance
        
        Returns:
            Dictionary with sector performance
        """
        try:
            # Define sector-wise stock lists (simplified)
            sectors = {
                'Banking': ['HDFCBANK.NS', 'ICICIBANK.NS', 'SBIN.NS', 'AXISBANK.NS'],
                'IT': ['TCS.NS', 'INFY.NS', 'WIPRO.NS', 'HCLTECH.NS'],
                'Oil & Gas': ['RELIANCE.NS', 'ONGC.NS', 'IOC.NS'],
                'FMCG': ['HINDUNILVR.NS', 'ITC.NS', 'MARICO.NS'],
                'Auto': ['MARUTI.NS', 'TATAMOTORS.NS', 'M&M.NS']
            }
            
            sector_performance = {}
            
            for sector, symbols in sectors.items():
                sector_return = 0
                valid_stocks = 0
                
                for symbol in symbols:
                    try:
                        data = self.get_stock_data(symbol, period="5d", interval="1d")
                        if not data.empty and len(data) >= 2:
                            current_price = data.iloc[-1]['close']
                            prev_price = data.iloc[-5]['close']
                            
                            if prev_price > 0:
                                stock_return = (current_price - prev_price) / prev_price
                                sector_return += stock_return
                                valid_stocks += 1
                                
                    except Exception as e:
                        logger.warning(f"Error processing {symbol}: {str(e)}")
                        continue
                
                if valid_stocks > 0:
                    sector_performance[sector] = (sector_return / valid_stocks) * 100
                else:
                    sector_performance[sector] = 0.0
            
            return sector_performance
            
        except Exception as e:
            logger.error(f"Error calculating sector performance: {str(e)}")
            return {}
    
    def get_live_quotes(self, symbols: List[str]) -> List[Dict]:
        """
        Get live quotes for multiple symbols
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            List of dictionaries with live quote data
        """
        quotes = []
        
        for symbol in symbols:
            try:
                data = self.get_stock_data(symbol, period="1d", interval="1m")
                if not data.empty:
                    latest = data.iloc[-1]
                    quote = {
                        'symbol': symbol,
                        'price': latest['close'],
                        'change': latest['close'] - data.iloc[0]['open'],
                        'change_percent': ((latest['close'] - data.iloc[0]['open']) / data.iloc[0]['open']) * 100,
                        'volume': latest['volume'],
                        'timestamp': latest['timestamp']
                    }
                    quotes.append(quote)
                    
            except Exception as e:
                logger.warning(f"Error getting quote for {symbol}: {str(e)}")
                continue
        
        return quotes
    
    def get_market_data(self) -> MarketData:
        """
        Get comprehensive market data
        
        Returns:
            MarketData object
        """
        try:
            # Get Nifty 50 and Sensex data
            nifty_data = self.get_stock_data('^NSEI', period="1d", interval="1d")
            sensex_data = self.get_stock_data('^BSESN', period="1d", interval="1d")
            
            nifty_50 = nifty_data.iloc[-1]['close'] if not nifty_data.empty else 0
            sensex = sensex_data.iloc[-1]['close'] if not sensex_data.empty else 0
            
            # Get market breadth
            breadth = self.get_market_breadth()
            advance_decline_ratio = breadth.get('advance_decline_ratio', 0)
            
            # Get sector performance
            sector_performance = self.get_sector_performance()
            
            # Simplified FII/DII data (would need actual API access)
            fii_net_inflow = 0.0
            dii_net_inflow = 0.0
            
            market_breadth = {
                'advances': breadth.get('advances', 0),
                'declines': breadth.get('declines', 0),
                'total': breadth.get('total_stocks', 0)
            }
            
            return MarketData(
                timestamp=datetime.now(),
                nifty_50=nifty_50,
                sensex=sensex,
                advance_decline_ratio=advance_decline_ratio,
                fii_net_inflow=fii_net_inflow,
                dii_net_inflow=dii_net_inflow,
                sector_performance=sector_performance,
                market_breadth=market_breadth
            )
            
        except Exception as e:
            logger.error(f"Error getting market data: {str(e)}")
            return MarketData(
                timestamp=datetime.now(),
                nifty_50=0,
                sensex=0,
                advance_decline_ratio=0,
                fii_net_inflow=0,
                dii_net_inflow=0,
                sector_performance={},
                market_breadth={}
            )


class WebScraper:
    """Web scraper for additional market data"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def get_market_news(self, source: str = "moneycontrol") -> List[Dict]:
        """
        Get market news from various sources
        
        Args:
            source: News source
            
        Returns:
            List of news items
        """
        news = []
        
        try:
            if source == "moneycontrol":
                url = "https://www.moneycontrol.com/news/business/markets/"
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                news_items = soup.find_all('div', class_='news_list')
                
                for item in news_items[:10]:  # Get top 10 news
                    try:
                        title_elem = item.find('h2')
                        link_elem = item.find('a')
                        
                        if title_elem and link_elem:
                            title = title_elem.get_text(strip=True)
                            link = link_elem.get('href', '')
                            
                            news.append({
                                'title': title,
                                'link': link,
                                'source': source,
                                'timestamp': datetime.now()
                            })
                    except Exception as e:
                        logger.warning(f"Error parsing news item: {str(e)}")
                        continue
                        
        except Exception as e:
            logger.error(f"Error scraping news from {source}: {str(e)}")
        
        return news
    
    def get_market_sentiment(self) -> Dict:
        """
        Get market sentiment indicators
        
        Returns:
            Dictionary with sentiment metrics
        """
        sentiment = {
            'fear_greed_index': 50,  # Placeholder
            'put_call_ratio': 1.0,   # Placeholder
            'vix': 20.0,             # Placeholder
            'timestamp': datetime.now()
        }
        
        return sentiment


# Global instances
market_data_provider = MarketDataProvider()
web_scraper = WebScraper() 