# Core data handling
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.0.0

# API and web scraping
requests>=2.31.0
httpx>=0.24.0
aiohttp>=3.8.0
beautifulsoup4>=4.12.0
selenium>=4.15.0

# Time-series forecasting and ML (Python 3.13 compatible)
scikit-learn>=1.3.0
# pmdarima>=2.0.0     # Commented - build issues with Python 3.13
# prophet>=1.1.0      # Commented - dependency issues with Python 3.13
# sktime>=0.22.0      # Commented - may have compatibility issues
# darts>=0.22.0       # Commented - dependency issues with Python 3.13
# autots>=0.5.0       # Commented - may have compatibility issues

# Technical analysis and backtesting
pandas-ta==0.3.14b0  # Using beta version that's available
backtrader>=1.9.0

# Market data
yfinance>=0.2.0
# nsepy>=0.8.0        # Commented - build issues

# Orchestration and app
fastapi>=0.104.0
streamlit>=1.28.0
apscheduler>=3.10.0

# Database
sqlalchemy>=2.0.0
# sqlite3 is built into Python, no need to install

# MLOps and evaluation (basic versions)
# mlflow>=2.7.0       # Commented - complex dependencies
optuna>=3.4.0
# shap>=0.43.0        # Commented - may have build issues

# Visualization
matplotlib>=3.7.0
plotly>=5.17.0
seaborn>=0.12.0

# Utilities
python-dotenv>=1.0.0
loguru>=0.7.0

# Additional useful packages for stock analysis
scipy>=1.10.0
statsmodels>=0.14.0