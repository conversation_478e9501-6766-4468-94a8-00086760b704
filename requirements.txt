# Core data handling
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.0.0

# API and web scraping
requests>=2.31.0
httpx>=0.24.0
aiohttp>=3.8.0
beautifulsoup4>=4.12.0
selenium>=4.15.0

# Machine Learning & AI for Stock Prediction
scikit-learn>=1.3.0
tensorflow>=2.15.0
keras>=3.0.0
torch>=2.1.0
xgboost>=2.0.0
lightgbm>=4.0.0

# Time-series forecasting (Python 3.13 compatible)
statsmodels>=0.14.0
scipy>=1.10.0
# pmdarima>=2.0.0     # Will add when Python 3.13 support available
# prophet>=1.1.0      # Will add when Python 3.13 support available

# Technical analysis and backtesting
pandas-ta==0.3.14b0  # Using beta version that's available
backtrader>=1.9.0
ta-lib>=0.4.0  # Technical Analysis Library

# Market data and financial APIs
yfinance>=0.2.0
alpha-vantage>=2.3.0
finnhub-python>=2.4.0
# nsepy>=0.8.0        # Will add when build issues resolved

# AI Agents and LLM Integration
openai>=1.0.0
langchain>=0.1.0
langchain-community>=0.0.10
transformers>=4.35.0

# Orchestration and app
fastapi>=0.104.0
streamlit>=1.28.0
streamlit-option-menu>=0.3.0
streamlit-plotly-events>=0.0.6
apscheduler>=3.10.0

# Database and caching
sqlalchemy>=2.0.0
redis>=5.0.0
# sqlite3 is built into Python

# MLOps and evaluation
optuna>=3.4.0
wandb>=0.16.0  # Weights & Biases for experiment tracking
joblib>=1.3.0  # Model persistence
# mlflow>=2.7.0       # Will add when dependencies resolved
# shap>=0.43.0        # Will add when build issues resolved

# Visualization and dashboards
matplotlib>=3.7.0
plotly>=5.17.0
seaborn>=0.12.0
dash>=2.15.0
bokeh>=3.3.0

# Utilities and monitoring
python-dotenv>=1.0.0
loguru>=0.7.0
schedule>=1.2.0
psutil>=5.9.0
tqdm>=4.66.0

# Financial calculations and risk management
quantlib>=1.32.0  # Quantitative finance library
pyfolio>=0.9.2    # Portfolio and risk analytics
empyrical>=0.5.5  # Financial risk metrics

# News and sentiment analysis
newspaper3k>=0.2.8
textblob>=0.17.1
vaderSentiment>=3.3.2

# Additional data sources
fredapi>=0.5.0     # Federal Reserve Economic Data
quandl>=3.7.0      # Financial and economic data