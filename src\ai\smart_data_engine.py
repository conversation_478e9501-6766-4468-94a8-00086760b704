"""
AI-Powered Smart Market Data Engine
Intelligent data fetching with multiple sources, caching, and real-time updates
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import time
from dataclasses import dataclass, asdict
import json
import hashlib
from pathlib import Path

# Data sources
import yfinance as yf
try:
    from alpha_vantage.timeseries import TimeSeries
    from alpha_vantage.fundamentaldata import FundamentalData
    HAS_ALPHA_VANTAGE = True
except ImportError:
    HAS_ALPHA_VANTAGE = False

try:
    import finnhub
    HAS_FINNHUB = True
except ImportError:
    HAS_FINNHUB = False

# AI and NLP for news analysis
try:
    from textblob import TextBlob
    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
    HAS_SENTIMENT = True
except ImportError:
    HAS_SENTIMENT = False

# Caching and storage
try:
    import redis
    HAS_REDIS = True
except ImportError:
    HAS_REDIS = False

from loguru import logger
import requests
from bs4 import BeautifulSoup

@dataclass
class MarketData:
    """Comprehensive market data structure"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    adjusted_close: Optional[float] = None
    
@dataclass
class NewsData:
    """News data with sentiment analysis"""
    title: str
    content: str
    source: str
    timestamp: datetime
    sentiment_score: float
    sentiment_label: str
    relevance_score: float
    
@dataclass
class FundamentalData:
    """Fundamental analysis data"""
    symbol: str
    market_cap: Optional[float]
    pe_ratio: Optional[float]
    pb_ratio: Optional[float]
    debt_to_equity: Optional[float]
    roe: Optional[float]
    revenue_growth: Optional[float]
    earnings_growth: Optional[float]
    dividend_yield: Optional[float]
    beta: Optional[float]
    
@dataclass
class MarketSentiment:
    """Market sentiment indicators"""
    symbol: str
    overall_sentiment: float
    news_sentiment: float
    social_sentiment: float
    technical_sentiment: float
    fear_greed_index: float
    volatility_index: float

class DataSourceManager:
    """Manages multiple data sources with failover and load balancing"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.sources = {}
        self.source_health = {}
        self.rate_limits = {}
        self._initialize_sources()
        
    def _initialize_sources(self):
        """Initialize all available data sources"""
        # Yahoo Finance (free, reliable)
        self.sources['yahoo'] = {
            'priority': 1,
            'rate_limit': 2000,  # requests per hour
            'cost': 0,
            'reliability': 0.9
        }
        
        # Alpha Vantage (API key required)
        if HAS_ALPHA_VANTAGE and self.config.get('alpha_vantage_key'):
            self.sources['alpha_vantage'] = {
                'priority': 2,
                'rate_limit': 500,  # requests per day for free tier
                'cost': 0,
                'reliability': 0.95,
                'client': TimeSeries(key=self.config['alpha_vantage_key']),
                'fundamental_client': FundamentalData(key=self.config['alpha_vantage_key'])
            }
        
        # Finnhub (API key required)
        if HAS_FINNHUB and self.config.get('finnhub_key'):
            self.sources['finnhub'] = {
                'priority': 3,
                'rate_limit': 60,  # requests per minute for free tier
                'cost': 0,
                'reliability': 0.92,
                'client': finnhub.Client(api_key=self.config['finnhub_key'])
            }
        
        # Initialize health status
        for source in self.sources:
            self.source_health[source] = {'status': 'healthy', 'last_check': datetime.now()}
            self.rate_limits[source] = {'count': 0, 'reset_time': datetime.now() + timedelta(hours=1)}
    
    def get_best_source(self, data_type: str = 'price') -> str:
        """Select best available source based on health, rate limits, and priority"""
        available_sources = []
        
        for source, info in self.sources.items():
            # Check health
            if self.source_health[source]['status'] != 'healthy':
                continue
                
            # Check rate limits
            if self._is_rate_limited(source):
                continue
                
            # Check if source supports data type
            if data_type == 'fundamental' and source not in ['alpha_vantage', 'finnhub']:
                continue
                
            available_sources.append((source, info['priority']))
        
        if not available_sources:
            logger.warning("No healthy data sources available")
            return 'yahoo'  # Fallback to Yahoo Finance
        
        # Return source with highest priority (lowest number)
        return min(available_sources, key=lambda x: x[1])[0]
    
    def _is_rate_limited(self, source: str) -> bool:
        """Check if source is rate limited"""
        limit_info = self.rate_limits[source]
        
        # Reset counter if time window passed
        if datetime.now() > limit_info['reset_time']:
            limit_info['count'] = 0
            limit_info['reset_time'] = datetime.now() + timedelta(hours=1)
        
        return limit_info['count'] >= self.sources[source]['rate_limit']
    
    def increment_rate_limit(self, source: str):
        """Increment rate limit counter for source"""
        self.rate_limits[source]['count'] += 1

class SmartCache:
    """Intelligent caching system with TTL and smart invalidation"""
    
    def __init__(self, use_redis: bool = False, redis_config: Optional[Dict] = None):
        self.use_redis = use_redis and HAS_REDIS
        self.local_cache = {}
        self.cache_stats = {'hits': 0, 'misses': 0}
        
        if self.use_redis:
            try:
                self.redis_client = redis.Redis(**redis_config) if redis_config else redis.Redis()
                self.redis_client.ping()  # Test connection
                logger.info("Redis cache initialized successfully")
            except Exception as e:
                logger.warning(f"Redis connection failed: {e}. Using local cache.")
                self.use_redis = False
    
    def _generate_key(self, symbol: str, data_type: str, timeframe: str, **kwargs) -> str:
        """Generate cache key"""
        key_data = f"{symbol}:{data_type}:{timeframe}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, symbol: str, data_type: str, timeframe: str, **kwargs) -> Optional[Any]:
        """Get data from cache"""
        key = self._generate_key(symbol, data_type, timeframe, **kwargs)
        
        try:
            if self.use_redis:
                data = self.redis_client.get(key)
                if data:
                    self.cache_stats['hits'] += 1
                    return json.loads(data)
            else:
                if key in self.local_cache:
                    cache_entry = self.local_cache[key]
                    if datetime.now() < cache_entry['expires']:
                        self.cache_stats['hits'] += 1
                        return cache_entry['data']
                    else:
                        del self.local_cache[key]
        except Exception as e:
            logger.error(f"Cache get error: {e}")
        
        self.cache_stats['misses'] += 1
        return None
    
    def set(self, symbol: str, data_type: str, timeframe: str, data: Any, ttl: int = 3600, **kwargs):
        """Set data in cache"""
        key = self._generate_key(symbol, data_type, timeframe, **kwargs)
        
        try:
            if self.use_redis:
                self.redis_client.setex(key, ttl, json.dumps(data, default=str))
            else:
                self.local_cache[key] = {
                    'data': data,
                    'expires': datetime.now() + timedelta(seconds=ttl)
                }
        except Exception as e:
            logger.error(f"Cache set error: {e}")
    
    def invalidate_pattern(self, pattern: str):
        """Invalidate cache entries matching pattern"""
        try:
            if self.use_redis:
                keys = self.redis_client.keys(pattern)
                if keys:
                    self.redis_client.delete(*keys)
            else:
                keys_to_delete = [k for k in self.local_cache.keys() if pattern in k]
                for key in keys_to_delete:
                    del self.local_cache[key]
        except Exception as e:
            logger.error(f"Cache invalidation error: {e}")

class NewsAnalyzer:
    """AI-powered news analysis and sentiment extraction"""
    
    def __init__(self):
        self.sentiment_analyzer = SentimentIntensityAnalyzer() if HAS_SENTIMENT else None
        self.news_sources = [
            'https://economictimes.indiatimes.com/markets/stocks',
            'https://www.moneycontrol.com/news/business/stocks/',
            'https://www.business-standard.com/markets',
        ]
    
    async def fetch_news(self, symbol: str, limit: int = 10) -> List[NewsData]:
        """Fetch and analyze news for a symbol"""
        news_items = []
        
        try:
            # Fetch from multiple sources
            async with aiohttp.ClientSession() as session:
                tasks = [self._fetch_from_source(session, source, symbol) for source in self.news_sources]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, list):
                        news_items.extend(result)
        
        except Exception as e:
            logger.error(f"News fetching error: {e}")
        
        # Sort by relevance and timestamp
        news_items.sort(key=lambda x: (x.relevance_score, x.timestamp), reverse=True)
        return news_items[:limit]
    
    async def _fetch_from_source(self, session: aiohttp.ClientSession, source: str, symbol: str) -> List[NewsData]:
        """Fetch news from a specific source"""
        try:
            async with session.get(source, timeout=10) as response:
                if response.status == 200:
                    html = await response.text()
                    return self._parse_news(html, source, symbol)
        except Exception as e:
            logger.error(f"Error fetching from {source}: {e}")
        
        return []
    
    def _parse_news(self, html: str, source: str, symbol: str) -> List[NewsData]:
        """Parse news from HTML content"""
        news_items = []
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Generic news extraction (would need customization per source)
            articles = soup.find_all(['article', 'div'], class_=lambda x: x and 'news' in x.lower())
            
            for article in articles[:5]:  # Limit per source
                title_elem = article.find(['h1', 'h2', 'h3', 'a'])
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    
                    # Check relevance to symbol
                    relevance = self._calculate_relevance(title, symbol)
                    if relevance > 0.3:  # Minimum relevance threshold
                        
                        content = article.get_text(strip=True)[:500]  # First 500 chars
                        sentiment = self._analyze_sentiment(title + " " + content)
                        
                        news_items.append(NewsData(
                            title=title,
                            content=content,
                            source=source,
                            timestamp=datetime.now(),
                            sentiment_score=sentiment['compound'],
                            sentiment_label=sentiment['label'],
                            relevance_score=relevance
                        ))
        
        except Exception as e:
            logger.error(f"News parsing error: {e}")
        
        return news_items
    
    def _calculate_relevance(self, text: str, symbol: str) -> float:
        """Calculate relevance of news to symbol"""
        text_lower = text.lower()
        symbol_lower = symbol.lower().replace('.ns', '').replace('.bo', '')
        
        # Simple keyword matching (can be enhanced with NLP)
        if symbol_lower in text_lower:
            return 1.0
        
        # Company name mapping (simplified)
        company_keywords = {
            'tcs': ['tata consultancy', 'tcs'],
            'reliance': ['reliance', 'ril'],
            'hdfcbank': ['hdfc bank', 'hdfc'],
            'infy': ['infosys'],
            'icicibank': ['icici bank', 'icici']
        }
        
        if symbol_lower in company_keywords:
            for keyword in company_keywords[symbol_lower]:
                if keyword in text_lower:
                    return 0.8
        
        # Market-related keywords
        market_keywords = ['stock', 'share', 'market', 'trading', 'investment']
        relevance = sum(0.1 for keyword in market_keywords if keyword in text_lower)
        
        return min(relevance, 1.0)
    
    def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of text"""
        if not self.sentiment_analyzer:
            return {'compound': 0.0, 'label': 'neutral'}
        
        try:
            scores = self.sentiment_analyzer.polarity_scores(text)
            
            # Determine label
            if scores['compound'] >= 0.05:
                label = 'positive'
            elif scores['compound'] <= -0.05:
                label = 'negative'
            else:
                label = 'neutral'
            
            return {
                'compound': scores['compound'],
                'positive': scores['pos'],
                'negative': scores['neg'],
                'neutral': scores['neu'],
                'label': label
            }
        
        except Exception as e:
            logger.error(f"Sentiment analysis error: {e}")
            return {'compound': 0.0, 'label': 'neutral'}

class SmartDataEngine:
    """Main AI-powered data engine"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.source_manager = DataSourceManager(config)
        self.cache = SmartCache(
            use_redis=config.get('use_redis', False),
            redis_config=config.get('redis_config')
        )
        self.news_analyzer = NewsAnalyzer()
        self.data_quality_threshold = 0.95
        
    async def get_market_data(self, symbol: str, period: str = '1y', interval: str = '1d') -> pd.DataFrame:
        """Get comprehensive market data with intelligent source selection"""
        
        # Check cache first
        cached_data = self.cache.get(symbol, 'market_data', f"{period}_{interval}")
        if cached_data is not None:
            logger.info(f"Cache hit for {symbol} market data")
            return pd.DataFrame(cached_data)
        
        # Get best source
        source = self.source_manager.get_best_source('price')
        logger.info(f"Using {source} for {symbol} market data")
        
        try:
            if source == 'yahoo':
                data = await self._fetch_yahoo_data(symbol, period, interval)
            elif source == 'alpha_vantage':
                data = await self._fetch_alpha_vantage_data(symbol, period, interval)
            elif source == 'finnhub':
                data = await self._fetch_finnhub_data(symbol, period, interval)
            else:
                raise ValueError(f"Unknown source: {source}")
            
            # Validate data quality
            if self._validate_data_quality(data):
                # Cache successful result
                cache_ttl = 300 if interval in ['1m', '5m'] else 3600  # 5 min for intraday, 1 hour for daily
                self.cache.set(symbol, 'market_data', f"{period}_{interval}", data.to_dict(), cache_ttl)
                
                self.source_manager.increment_rate_limit(source)
                return data
            else:
                logger.warning(f"Data quality check failed for {symbol} from {source}")
                # Try fallback source
                return await self._fetch_fallback_data(symbol, period, interval)
                
        except Exception as e:
            logger.error(f"Error fetching data from {source}: {e}")
            return await self._fetch_fallback_data(symbol, period, interval)
    
    async def _fetch_yahoo_data(self, symbol: str, period: str, interval: str) -> pd.DataFrame:
        """Fetch data from Yahoo Finance"""
        ticker = yf.Ticker(symbol)
        data = ticker.history(period=period, interval=interval)
        
        if data.empty:
            raise ValueError("No data returned from Yahoo Finance")
        
        # Standardize column names
        data.columns = [col.lower() for col in data.columns]
        data.reset_index(inplace=True)
        
        return data
    
    async def _fetch_alpha_vantage_data(self, symbol: str, period: str, interval: str) -> pd.DataFrame:
        """Fetch data from Alpha Vantage"""
        if not HAS_ALPHA_VANTAGE:
            raise ImportError("Alpha Vantage not available")
        
        client = self.source_manager.sources['alpha_vantage']['client']
        
        # Map intervals
        av_interval = {'1d': 'daily', '1wk': 'weekly', '1mo': 'monthly'}.get(interval, 'daily')
        
        if av_interval == 'daily':
            data, _ = client.get_daily_adjusted(symbol, outputsize='full')
        elif av_interval == 'weekly':
            data, _ = client.get_weekly_adjusted(symbol)
        else:
            data, _ = client.get_monthly_adjusted(symbol)
        
        df = pd.DataFrame(data).T
        df.index = pd.to_datetime(df.index)
        df = df.astype(float)
        
        # Standardize column names
        df.columns = ['open', 'high', 'low', 'close', 'adjusted_close', 'volume', 'dividend', 'split']
        df.reset_index(inplace=True)
        df.rename(columns={'index': 'date'}, inplace=True)
        
        return df
    
    async def _fetch_finnhub_data(self, symbol: str, period: str, interval: str) -> pd.DataFrame:
        """Fetch data from Finnhub"""
        if not HAS_FINNHUB:
            raise ImportError("Finnhub not available")
        
        client = self.source_manager.sources['finnhub']['client']
        
        # Calculate date range
        end_date = datetime.now()
        period_map = {'1d': 1, '5d': 5, '1mo': 30, '3mo': 90, '6mo': 180, '1y': 365, '2y': 730}
        days = period_map.get(period, 365)
        start_date = end_date - timedelta(days=days)
        
        # Convert to Unix timestamps
        start_ts = int(start_date.timestamp())
        end_ts = int(end_date.timestamp())
        
        # Map intervals
        resolution_map = {'1m': '1', '5m': '5', '15m': '15', '30m': '30', '1h': '60', '1d': 'D'}
        resolution = resolution_map.get(interval, 'D')
        
        data = client.stock_candles(symbol, resolution, start_ts, end_ts)
        
        if data['s'] != 'ok':
            raise ValueError("No data returned from Finnhub")
        
        df = pd.DataFrame({
            'timestamp': pd.to_datetime(data['t'], unit='s'),
            'open': data['o'],
            'high': data['h'],
            'low': data['l'],
            'close': data['c'],
            'volume': data['v']
        })
        
        return df
    
    async def _fetch_fallback_data(self, symbol: str, period: str, interval: str) -> pd.DataFrame:
        """Fallback data fetching using Yahoo Finance"""
        try:
            return await self._fetch_yahoo_data(symbol, period, interval)
        except Exception as e:
            logger.error(f"Fallback data fetch failed: {e}")
            # Return empty DataFrame with proper structure
            return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    
    def _validate_data_quality(self, data: pd.DataFrame) -> bool:
        """Validate data quality"""
        if data.empty:
            return False
        
        # Check for required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_cols):
            return False
        
        # Check for excessive missing values
        missing_ratio = data.isnull().sum().sum() / (len(data) * len(data.columns))
        if missing_ratio > (1 - self.data_quality_threshold):
            return False
        
        # Check for data consistency
        if (data['high'] < data['low']).any():
            return False
        
        if (data['high'] < data['close']).any() or (data['low'] > data['close']).any():
            return False
        
        return True
    
    async def get_fundamental_data(self, symbol: str) -> FundamentalData:
        """Get fundamental analysis data"""
        # Check cache
        cached_data = self.cache.get(symbol, 'fundamental', 'latest')
        if cached_data:
            return FundamentalData(**cached_data)
        
        try:
            # Try Yahoo Finance first
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            fundamental_data = FundamentalData(
                symbol=symbol,
                market_cap=info.get('marketCap'),
                pe_ratio=info.get('trailingPE'),
                pb_ratio=info.get('priceToBook'),
                debt_to_equity=info.get('debtToEquity'),
                roe=info.get('returnOnEquity'),
                revenue_growth=info.get('revenueGrowth'),
                earnings_growth=info.get('earningsGrowth'),
                dividend_yield=info.get('dividendYield'),
                beta=info.get('beta')
            )
            
            # Cache for 24 hours
            self.cache.set(symbol, 'fundamental', 'latest', asdict(fundamental_data), 86400)
            
            return fundamental_data
            
        except Exception as e:
            logger.error(f"Error fetching fundamental data: {e}")
            return FundamentalData(symbol=symbol)
    
    async def get_market_sentiment(self, symbol: str) -> MarketSentiment:
        """Get comprehensive market sentiment analysis"""
        # Check cache
        cached_sentiment = self.cache.get(symbol, 'sentiment', 'latest')
        if cached_sentiment:
            return MarketSentiment(**cached_sentiment)
        
        try:
            # Fetch news and analyze sentiment
            news_items = await self.news_analyzer.fetch_news(symbol)
            
            # Calculate news sentiment
            if news_items:
                news_sentiment = np.mean([item.sentiment_score for item in news_items])
            else:
                news_sentiment = 0.0
            
            # Get technical sentiment (simplified)
            market_data = await self.get_market_data(symbol, period='1mo', interval='1d')
            technical_sentiment = self._calculate_technical_sentiment(market_data)
            
            # Overall sentiment (weighted average)
            overall_sentiment = (news_sentiment * 0.4 + technical_sentiment * 0.6)
            
            sentiment_data = MarketSentiment(
                symbol=symbol,
                overall_sentiment=overall_sentiment,
                news_sentiment=news_sentiment,
                social_sentiment=0.0,  # Would integrate social media APIs
                technical_sentiment=technical_sentiment,
                fear_greed_index=0.5,  # Would integrate fear & greed index
                volatility_index=market_data['close'].pct_change().std() if not market_data.empty else 0.0
            )
            
            # Cache for 1 hour
            self.cache.set(symbol, 'sentiment', 'latest', asdict(sentiment_data), 3600)
            
            return sentiment_data
            
        except Exception as e:
            logger.error(f"Error calculating market sentiment: {e}")
            return MarketSentiment(
                symbol=symbol,
                overall_sentiment=0.0,
                news_sentiment=0.0,
                social_sentiment=0.0,
                technical_sentiment=0.0,
                fear_greed_index=0.5,
                volatility_index=0.0
            )
    
    def _calculate_technical_sentiment(self, data: pd.DataFrame) -> float:
        """Calculate technical sentiment from price data"""
        if data.empty or len(data) < 20:
            return 0.0
        
        try:
            # Simple technical sentiment based on moving averages and momentum
            data['sma_20'] = data['close'].rolling(20).mean()
            data['sma_5'] = data['close'].rolling(5).mean()
            
            # Price above/below moving averages
            price_vs_sma20 = 1 if data['close'].iloc[-1] > data['sma_20'].iloc[-1] else -1
            price_vs_sma5 = 1 if data['close'].iloc[-1] > data['sma_5'].iloc[-1] else -1
            
            # Short-term vs long-term MA
            ma_trend = 1 if data['sma_5'].iloc[-1] > data['sma_20'].iloc[-1] else -1
            
            # Recent price momentum
            recent_return = (data['close'].iloc[-1] / data['close'].iloc[-5] - 1) if len(data) >= 5 else 0
            momentum = 1 if recent_return > 0.02 else (-1 if recent_return < -0.02 else 0)
            
            # Combine signals
            sentiment = (price_vs_sma20 * 0.3 + price_vs_sma5 * 0.2 + ma_trend * 0.3 + momentum * 0.2)
            
            # Normalize to [-1, 1]
            return max(-1, min(1, sentiment))
            
        except Exception as e:
            logger.error(f"Technical sentiment calculation error: {e}")
            return 0.0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        total_requests = self.cache.cache_stats['hits'] + self.cache.cache_stats['misses']
        hit_rate = self.cache.cache_stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            'cache_hits': self.cache.cache_stats['hits'],
            'cache_misses': self.cache.cache_stats['misses'],
            'hit_rate': hit_rate,
            'source_health': self.source_manager.source_health,
            'rate_limits': self.source_manager.rate_limits
        }
