# 🚀 Quick Start Guide

Get your Indian Stock Trading & Mutual Fund Risk Analysis System up and running in minutes!

## 📋 Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- Internet connection for market data

## ⚡ Quick Installation

### 1. <PERSON>lone and Setup
```bash
# Clone the repository
git clone <your-repo-url>
cd Stockss

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Run the Dashboard
```bash
streamlit run src/ui/main.py
```

The dashboard will open in your browser at `http://localhost:8501`

## 🎯 Quick Demo

Want to see the system in action without the full dashboard?

```bash
python example_usage.py
```

This will run through all the main features and show you what the system can do.

## 📊 What You'll Get

### 🏠 Dashboard Overview
- Real-time Nifty 50 and Sensex data
- Market breadth analysis
- Sector performance tracking
- Latest market news

### 📈 Intraday Trading Module
- Technical indicators (RSI, MACD, VWAP)
- Trading signals with confidence levels
- India-specific compliance checks
- Short-selling eligibility verification
- Margin and leverage calculations

### 💰 Buffett-Style Analysis
- Fundamental stock screening
- Quality, valuation, and moat scoring
- Sector-specific criteria adjustments
- Investment recommendations

### 🏦 Mutual Fund Risk Analysis
- SEBI/AMFI Riskometer compliance
- Six-level risk classification
- Asset allocation analysis
- Risk factor identification

## 🔧 Configuration

### Environment Variables
Copy the example environment file and customize:
```bash
cp .env.example .env
# Edit .env with your preferences
```

### Key Settings
- **Broker Configuration**: Set your broker's auto square-off time and margin requirements
- **Risk Tolerance**: Choose Conservative, Moderate, or Aggressive
- **Analysis Parameters**: Adjust RSI, MACD, and moving average periods

## 📱 Using the Dashboard

### 1. **Dashboard Overview**
- View market summary and sector performance
- Check market breadth and news

### 2. **Intraday Trading**
- Enter stock symbol (e.g., TCS, RELIANCE)
- Select timeframe (1m, 5m, 15m, 30m, 1h)
- Get technical analysis and trading signals
- Check compliance rules

### 3. **Buffett Analysis**
- Enter stock symbol for fundamental analysis
- View quality, valuation, and moat scores
- Get investment recommendations

### 4. **Mutual Fund Risk**
- Enter fund name or ISIN
- View SEBI/AMFI Riskometer level
- Analyze asset allocation and risk factors

## 🚨 Important Notes

### ⚠️ Disclaimer
- This is **NOT financial advice**
- Always consult qualified financial advisors
- Past performance doesn't guarantee future results
- Trading involves risk

### 🔒 Compliance
- System enforces India-specific trading rules
- Short-selling compliance with SLB framework
- SEBI peak margin regime compliance
- Auto square-off timing warnings

### 📊 Data Sources
- **Stock Data**: Yahoo Finance (free tier)
- **Fundamental Data**: Company financials via yfinance
- **Mutual Fund Data**: Sample data (integrate with AMFI APIs for production)

## 🐛 Troubleshooting

### Common Issues

**1. Import Errors**
```bash
# Ensure you're in the right directory
cd Stockss
# Check Python path
python -c "import sys; print(sys.path)"
```

**2. Missing Dependencies**
```bash
# Reinstall requirements
pip install -r requirements.txt --force-reinstall
```

**3. Streamlit Issues**
```bash
# Update Streamlit
pip install --upgrade streamlit
# Clear cache
streamlit cache clear
```

**4. Market Data Issues**
- Check internet connection
- Verify API rate limits
- Some data may be delayed or unavailable

## 🔄 Next Steps

### For Development
1. **Add More Data Sources**: Integrate with NSE/BSE APIs
2. **Enhance Models**: Add machine learning capabilities
3. **Backtesting**: Implement strategy backtesting
4. **Real-time Alerts**: Add notification system

### For Production
1. **Database**: Switch to PostgreSQL
2. **Authentication**: Add user management
3. **API**: Create FastAPI backend
4. **Deployment**: Docker containerization

## 📞 Support

- **Issues**: Create GitHub issues
- **Documentation**: Check README.md
- **Examples**: Run `example_usage.py`

## 🎉 You're Ready!

Your Indian Stock Trading & Mutual Fund Risk Analysis System is now running! 

Start with the Dashboard Overview to see market data, then explore the Intraday Trading module for technical analysis, or dive into Buffett Analysis for fundamental insights.

Happy analyzing! 📈🇮🇳 