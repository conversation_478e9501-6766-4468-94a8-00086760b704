# Environment Configuration for Indian Stock Trading & MF Risk Analysis System

# API Keys (if using paid services)
YAHOO_FINANCE_API_KEY=your_yahoo_finance_api_key_here
NSE_API_KEY=your_nse_api_key_here
BSE_API_KEY=your_bse_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///./stock_analysis.db
# For PostgreSQL: postgresql://user:password@localhost:5432/stockdb

# Broker Configuration
BROKER_NAME=Default
AUTO_SQUARE_OFF_TIME=15:15
MARGIN_REQUIREMENT=0.20
MAX_LEVERAGE=5.0

# Analysis Parameters
RSI_PERIOD=14
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9
SMA_SHORT=20
SMA_LONG=50

# Risk Tolerance
RISK_TOLERANCE=Moderate

# Market Data Settings
CACHE_TTL=60
UPDATE_INTERVAL=300

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/stock_analysis.log

# Web Scraping
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
REQUEST_TIMEOUT=10

# Compliance Settings
SHORT_SELLING_ENABLED=true
SLB_ENABLED=true
DELIVERY_OBLIGATION=true
OVERNIGHT_SHORT_ALLOWED=false

# Mutual Fund Settings
RISKOMETER_ENABLED=true
MONTHLY_EVALUATION=true
SEBI_COMPLIANCE=true 