"""
Data models for the stock trading system
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, <PERSON>, validator
from enum import Enum


class MarketType(str, Enum):
    """Market types"""
    CASH = "cash"
    FUTURES = "futures"
    OPTIONS = "options"


class OrderType(str, Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    STOP_LIMIT = "stop_limit"


class PositionSide(str, Enum):
    """Position sides"""
    LONG = "long"
    SHORT = "short"


class RiskLevel(str, Enum):
    """SEBI/AMFI Riskometer levels"""
    VERY_LOW = "Very Low"
    LOW = "Low"
    MODERATE = "Moderate"
    MODERATELY_HIGH = "Moderately High"
    HIGH = "High"
    VERY_HIGH = "Very High"


class StockData(BaseModel):
    """Stock market data model"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    vwap: Optional[float] = None
    
    @validator('close')
    def close_must_be_positive(cls, v):
        if v <= 0:
            raise ValueError('Close price must be positive')
        return v


class TechnicalIndicators(BaseModel):
    """Technical indicators model"""
    symbol: str
    timestamp: datetime
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    atr: Optional[float] = None
    upper_band: Optional[float] = None
    lower_band: Optional[float] = None


class FundamentalData(BaseModel):
    """Fundamental data model"""
    symbol: str
    timestamp: datetime
    market_cap: Optional[float] = None
    pe_ratio: Optional[float] = None
    pb_ratio: Optional[float] = None
    debt_to_equity: Optional[float] = None
    roe: Optional[float] = None
    roic: Optional[float] = None
    revenue_growth: Optional[float] = None
    profit_growth: Optional[float] = None
    dividend_yield: Optional[float] = None
    beta: Optional[float] = None


class TradingSignal(BaseModel):
    """Trading signal model"""
    symbol: str
    timestamp: datetime
    signal_type: str  # "buy", "sell", "hold"
    confidence: float = Field(ge=0.0, le=1.0)
    price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    position_size: Optional[float] = None
    reasoning: List[str] = []
    risk_score: float = Field(ge=0.0, le=10.0)


class Position(BaseModel):
    """Trading position model"""
    symbol: str
    side: PositionSide
    quantity: int
    entry_price: float
    current_price: float
    entry_time: datetime
    pnl: float
    pnl_percentage: float
    margin_used: float
    leverage: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None


class MutualFundData(BaseModel):
    """Mutual fund data model"""
    fund_name: str
    isin: str
    nav: float
    nav_date: datetime
    expense_ratio: float
    risk_level: RiskLevel
    category: str
    asset_allocation: Dict[str, float]  # equity, debt, others
    market_cap_exposure: Dict[str, float]  # large, mid, small
    sector_exposure: Dict[str, float]
    top_holdings: List[Dict[str, Any]]


class RiskMetrics(BaseModel):
    """Risk metrics model"""
    symbol: str
    timestamp: datetime
    volatility: float
    beta: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    var_95: float  # Value at Risk 95%
    expected_shortfall: float
    downside_deviation: float


class ComplianceRules(BaseModel):
    """India-specific compliance rules"""
    short_selling_allowed: bool
    slb_eligible: bool
    margin_requirement: float = 0.20  # 20% minimum
    auto_square_off_time: str = "15:15"  # 3:15 PM
    circuit_limit: float
    delivery_obligation: bool
    overnight_short_allowed: bool = False  # For retail cash market


class MarketData(BaseModel):
    """Market-wide data"""
    timestamp: datetime
    nifty_50: float
    sensex: float
    advance_decline_ratio: float
    fii_net_inflow: float
    dii_net_inflow: float
    sector_performance: Dict[str, float]
    market_breadth: Dict[str, int] 