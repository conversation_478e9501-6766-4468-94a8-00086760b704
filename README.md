# Indian Stock Trading & Mutual Fund Risk Analysis System

A comprehensive Python-based system for Indian stock market analysis, intraday trading assistance, and mutual fund risk assessment.

## 🚀 Features

### 1. Intraday Trading Module
- **Short Selling Analysis**: India-specific SLB framework compliance
- **Margin & Leverage Calculator**: SEBI peak margin regime compliance
- **Live Market Data**: Real-time quotes, volume, and technical indicators
- **Risk Management**: Auto square-off timing, circuit-limit warnings
- **Technical Signals**: RSI, MACD, VWAP, Moving Averages, Breakouts

### 2. Daily Stock Selection & Buffett-Style Analysis
- **Fundamental Screening**: PE Ratio, ROE, Debt-to-Equity, Growth metrics
- **Quality Assessment**: 5-year financial stability, moat analysis
- **Valuation Metrics**: Sector comparisons, growth vs. value analysis
- **Morning Scanner**: Liquidity, volatility, and trend filters

### 3. Mutual Fund Risk Calculator & Predictor
- **SEBI/AMFI Riskometer**: Six-level risk classification
- **Portfolio Analysis**: Market cap exposure, sector allocation
- **Risk Metrics**: Volatility, Beta, Sharpe Ratio, Maximum Drawdown
- **Performance Prediction**: Time-series forecasting with uncertainty bands

## 🏗️ Architecture

```
src/
├── data/           # Data ingestion and storage
├── features/       # Feature engineering
├── models/         # ML models and forecasting
├── strategies/     # Trading strategies
├── risk/           # Risk management engine
├── compliance/     # India-specific market rules
├── backtesting/    # Strategy validation
└── ui/            # Streamlit dashboard
```

## 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd Stockss
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env with your API keys and configuration
```

## 🚀 Quick Start

1. **Run the Streamlit dashboard**
```bash
streamlit run src/ui/main.py
```

2. **Start the FastAPI backend**
```bash
uvicorn src.api.main:app --reload
```

## 📊 Data Sources

- **Stock Data**: NSE, BSE via yfinance and nsepy
- **Fundamental Data**: Company financials via yfinance
- **Mutual Fund Data**: AMFI, fund factsheets
- **Market News**: Web scraping from financial portals

## 🔒 Compliance Features

- **Short Selling**: SLB framework compliance, same-day square-off
- **Margin Rules**: SEBI peak margin regime (20% minimum)
- **Riskometer**: SEBI/AMFI six-level classification
- **Circuit Limits**: Auto square-off and risk warnings

## 📈 Key Algorithms

- **Technical Analysis**: RSI, MACD, VWAP, ATR, Moving Averages
- **Machine Learning**: Time-series forecasting, ensemble methods
- **Risk Models**: VaR, Expected Shortfall, regime detection
- **Portfolio Optimization**: Modern Portfolio Theory, risk parity

## 🧪 Testing & Validation

- **Backtesting**: Event-driven simulation with costs and slippage
- **Walk-Forward**: Out-of-sample validation
- **Performance Metrics**: Sharpe, Sortino, Calmar ratios
- **Risk Metrics**: Maximum drawdown, VaR, volatility clustering

## 📝 License

This project is for educational and research purposes. Please ensure compliance with local financial regulations.

## ⚠️ Disclaimer

This software is not financial advice. Trading involves risk, and past performance doesn't guarantee future results. Always consult with qualified financial advisors.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For questions and support, please open an issue in the repository. 