"""
Mutual Fund Risk Calculator and Predictor Module
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta

from ..data.models import MutualFundData, RiskLevel

logger = logging.getLogger(__name__)


@dataclass
class RiskometerParams:
    """SEBI/AMFI Riskometer parameters"""
    equity_weight: float = 0.0
    debt_weight: float = 0.0
    gold_weight: float = 0.0
    other_weight: float = 0.0
    market_cap_exposure: Dict[str, float] = None
    sector_concentration: float = 0.0
    credit_rating_exposure: Dict[str, float] = None
    duration_exposure: Dict[str, float] = None


class MutualFundRiskAnalyzer:
    """SEBI/AMFI Riskometer compliant mutual fund analyzer"""
    
    def __init__(self):
        # SEBI/AMFI Riskometer bands (2021 methodology)
        self.riskometer_bands = {
            'Very Low': {'min': 0, 'max': 20, 'color': '#00ff00'},
            'Low': {'min': 21, 'max': 40, 'color': '#90ee90'},
            'Moderate': {'min': 41, 'max': 60, 'color': '#ffff00'},
            'Moderately High': {'min': 61, 'max': 80, 'color': '#ffa500'},
            'High': {'min': 81, 'max': 90, 'color': '#ff4500'},
            'Very High': {'min': 91, 'max': 100, 'color': '#ff0000'}
        }
        
        # Market cap risk weights
        self.market_cap_risk_weights = {
            'Large Cap': 1.0,
            'Mid Cap': 1.5,
            'Small Cap': 2.0
        }
        
        # Credit rating risk weights
        self.credit_risk_weights = {
            'AAA': 1.0,
            'AA+': 1.1,
            'AA': 1.2,
            'AA-': 1.3,
            'A+': 1.4,
            'A': 1.5,
            'A-': 1.6,
            'BBB+': 1.7,
            'BBB': 1.8,
            'BBB-': 1.9,
            'BB+': 2.0,
            'BB': 2.1,
            'BB-': 2.2,
            'B+': 2.3,
            'B': 2.4,
            'B-': 2.5,
            'Below B-': 3.0
        }
        
        # Duration risk weights
        self.duration_risk_weights = {
            'Ultra Short': 1.0,
            'Short': 1.2,
            'Medium': 1.5,
            'Long': 2.0,
            'Ultra Long': 2.5
        }
    
    def calculate_equity_risk_score(self, fund_data: Dict) -> float:
        """
        Calculate equity risk score based on SEBI methodology
        
        Args:
            fund_data: Fund data including asset allocation and market cap exposure
            
        Returns:
            Equity risk score (0-100)
        """
        try:
            equity_weight = fund_data.get('asset_allocation', {}).get('equity', 0)
            
            if equity_weight == 0:
                return 0.0
            
            # Base equity risk score
            base_score = 30.0
            
            # Market cap exposure adjustment
            market_cap_score = 0.0
            market_cap_exposure = fund_data.get('market_cap_exposure', {})
            
            for cap_type, weight in market_cap_exposure.items():
                risk_weight = self.market_cap_risk_weights.get(cap_type, 1.0)
                market_cap_score += weight * risk_weight
            
            # Sector concentration adjustment
            sector_concentration = fund_data.get('sector_concentration', 0)
            sector_score = min(sector_concentration * 2, 20)  # Max 20 points
            
            # Calculate final equity risk score
            equity_risk_score = base_score + market_cap_score + sector_score
            
            # Normalize to 0-100 range
            equity_risk_score = min(max(equity_risk_score, 0), 100)
            
            return equity_risk_score
            
        except Exception as e:
            logger.error(f"Error calculating equity risk score: {str(e)}")
            return 50.0  # Default moderate risk
    
    def calculate_debt_risk_score(self, fund_data: Dict) -> float:
        """
        Calculate debt risk score based on SEBI methodology
        
        Args:
            fund_data: Fund data including debt exposure and credit ratings
            
        Returns:
            Debt risk score (0-100)
        """
        try:
            debt_weight = fund_data.get('asset_allocation', {}).get('debt', 0)
            
            if debt_weight == 0:
                return 0.0
            
            # Base debt risk score
            base_score = 20.0
            
            # Credit rating adjustment
            credit_score = 0.0
            credit_exposure = fund_data.get('credit_rating_exposure', {})
            
            for rating, weight in credit_exposure.items():
                risk_weight = self.credit_risk_weights.get(rating, 2.0)
                credit_score += weight * (risk_weight - 1.0) * 10
            
            # Duration adjustment
            duration_score = 0.0
            duration_exposure = fund_data.get('duration_exposure', {})
            
            for duration, weight in duration_exposure.items():
                risk_weight = self.duration_risk_weights.get(duration, 1.5)
                duration_score += weight * (risk_weight - 1.0) * 15
            
            # Calculate final debt risk score
            debt_risk_score = base_score + credit_score + duration_score
            
            # Normalize to 0-100 range
            debt_risk_score = min(max(debt_risk_score, 0), 100)
            
            return debt_risk_score
            
        except Exception as e:
            logger.error(f"Error calculating debt risk score: {str(e)}")
            return 30.0  # Default low-moderate risk
    
    def calculate_other_asset_risk_score(self, fund_data: Dict) -> float:
        """
        Calculate risk score for other assets (gold, commodities, etc.)
        
        Args:
            fund_data: Fund data including other asset allocations
            
        Returns:
            Other asset risk score (0-100)
        """
        try:
            other_weight = fund_data.get('asset_allocation', {}).get('others', 0)
            gold_weight = fund_data.get('asset_allocation', {}).get('gold', 0)
            
            if other_weight == 0 and gold_weight == 0:
                return 0.0
            
            # Gold risk (generally lower risk)
            gold_risk = gold_weight * 15.0
            
            # Other assets risk (higher risk)
            other_risk = other_weight * 40.0
            
            total_risk = gold_risk + other_risk
            
            return min(total_risk, 100)
            
        except Exception as e:
            logger.error(f"Error calculating other asset risk score: {str(e)}")
            return 25.0
    
    def calculate_composite_risk_score(self, fund_data: Dict) -> float:
        """
        Calculate composite risk score using SEBI methodology
        
        Args:
            fund_data: Fund data including all asset allocations
            
        Returns:
            Composite risk score (0-100)
        """
        try:
            # Calculate individual asset class risk scores
            equity_risk = self.calculate_equity_risk_score(fund_data)
            debt_risk = self.calculate_debt_risk_score(fund_data)
            other_risk = self.calculate_other_asset_risk_score(fund_data)
            
            # Get asset allocation weights
            asset_allocation = fund_data.get('asset_allocation', {})
            equity_weight = asset_allocation.get('equity', 0)
            debt_weight = asset_allocation.get('debt', 0)
            other_weight = asset_allocation.get('others', 0) + asset_allocation.get('gold', 0)
            
            # Calculate weighted composite risk score
            composite_score = (
                equity_risk * equity_weight +
                debt_risk * debt_weight +
                other_risk * other_weight
            )
            
            # Normalize to 0-100 range
            composite_score = min(max(composite_score, 0), 100)
            
            return composite_score
            
        except Exception as e:
            logger.error(f"Error calculating composite risk score: {str(e)}")
            return 50.0
    
    def map_to_riskometer_band(self, risk_score: float) -> Tuple[str, Dict]:
        """
        Map risk score to SEBI/AMFI Riskometer band
        
        Args:
            risk_score: Risk score (0-100)
            
        Returns:
            Tuple of (risk_level, band_details)
        """
        try:
            for level, band in self.riskometer_bands.items():
                if band['min'] <= risk_score <= band['max']:
                    return level, band
            
            # Default to moderate if no match
            return 'Moderate', self.riskometer_bands['Moderate']
            
        except Exception as e:
            logger.error(f"Error mapping to riskometer band: {str(e)}")
            return 'Moderate', self.riskometer_bands['Moderate']
    
    def calculate_volatility_metrics(self, nav_data: pd.Series) -> Dict[str, float]:
        """
        Calculate volatility and risk metrics
        
        Args:
            nav_data: Series of NAV values
            
        Returns:
            Dictionary with volatility metrics
        """
        try:
            if len(nav_data) < 2:
                return {}
            
            # Calculate returns
            returns = nav_data.pct_change().dropna()
            
            # Volatility metrics
            volatility = returns.std() * np.sqrt(252)  # Annualized
            max_drawdown = self._calculate_max_drawdown(nav_data)
            var_95 = np.percentile(returns, 5)  # 95% VaR
            expected_shortfall = returns[returns <= var_95].mean()
            
            # Downside deviation
            downside_returns = returns[returns < 0]
            downside_deviation = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
            
            return {
                'volatility': volatility,
                'max_drawdown': max_drawdown,
                'var_95': var_95,
                'expected_shortfall': expected_shortfall,
                'downside_deviation': downside_deviation
            }
            
        except Exception as e:
            logger.error(f"Error calculating volatility metrics: {str(e)}")
            return {}
    
    def _calculate_max_drawdown(self, nav_data: pd.Series) -> float:
        """
        Calculate maximum drawdown
        
        Args:
            nav_data: Series of NAV values
            
        Returns:
            Maximum drawdown percentage
        """
        try:
            peak = nav_data.expanding().max()
            drawdown = (nav_data - peak) / peak
            max_drawdown = drawdown.min()
            
            return abs(max_drawdown)
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {str(e)}")
            return 0.0
    
    def calculate_beta(self, fund_returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """
        Calculate fund beta relative to benchmark
        
        Args:
            fund_returns: Fund return series
            benchmark_returns: Benchmark return series
            
        Returns:
            Beta value
        """
        try:
            if len(fund_returns) < 2 or len(benchmark_returns) < 2:
                return 1.0
            
            # Align the series
            aligned_data = pd.concat([fund_returns, benchmark_returns], axis=1).dropna()
            
            if len(aligned_data) < 2:
                return 1.0
            
            fund_ret = aligned_data.iloc[:, 0]
            bench_ret = aligned_data.iloc[:, 1]
            
            # Calculate covariance and variance
            covariance = np.cov(fund_ret, bench_ret)[0, 1]
            benchmark_variance = np.var(bench_ret)
            
            if benchmark_variance == 0:
                return 1.0
            
            beta = covariance / benchmark_variance
            return beta
            
        except Exception as e:
            logger.error(f"Error calculating beta: {str(e)}")
            return 1.0
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.06) -> float:
        """
        Calculate Sharpe ratio
        
        Args:
            returns: Return series
            risk_free_rate: Risk-free rate (default 6% for India)
            
        Returns:
            Sharpe ratio
        """
        try:
            if len(returns) < 2:
                return 0.0
            
            excess_returns = returns - risk_free_rate/252  # Daily risk-free rate
            sharpe_ratio = excess_returns.mean() / returns.std() * np.sqrt(252)
            
            return sharpe_ratio
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {str(e)}")
            return 0.0
    
    def generate_risk_analysis(self, fund_data: Dict, nav_data: Optional[pd.Series] = None,
                              benchmark_data: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        Generate comprehensive risk analysis for a mutual fund
        
        Args:
            fund_data: Fund data including asset allocation
            nav_data: NAV data series (optional)
            benchmark_data: Benchmark data series (optional)
            
        Returns:
            Dictionary with complete risk analysis
        """
        try:
            # Calculate composite risk score
            composite_risk_score = self.calculate_composite_risk_score(fund_data)
            
            # Map to riskometer band
            risk_level, band_details = self.map_to_riskometer_band(composite_risk_score)
            
            # Calculate additional metrics if NAV data is available
            additional_metrics = {}
            if nav_data is not None:
                volatility_metrics = self.calculate_volatility_metrics(nav_data)
                additional_metrics.update(volatility_metrics)
                
                # Calculate returns
                returns = nav_data.pct_change().dropna()
                if len(returns) > 0:
                    additional_metrics['annualized_return'] = returns.mean() * 252
                    additional_metrics['sharpe_ratio'] = self.calculate_sharpe_ratio(returns)
            
            # Calculate beta if benchmark data is available
            if nav_data is not None and benchmark_data is not None:
                fund_returns = nav_data.pct_change().dropna()
                benchmark_returns = benchmark_data.pct_change().dropna()
                additional_metrics['beta'] = self.calculate_beta(fund_returns, benchmark_returns)
            
            # Generate risk analysis
            risk_analysis = {
                'fund_name': fund_data.get('fund_name', ''),
                'isin': fund_data.get('isin', ''),
                'analysis_date': datetime.now(),
                'composite_risk_score': composite_risk_score,
                'riskometer_level': risk_level,
                'riskometer_band': band_details,
                'asset_allocation': fund_data.get('asset_allocation', {}),
                'market_cap_exposure': fund_data.get('market_cap_exposure', {}),
                'sector_exposure': fund_data.get('sector_exposure', {}),
                'risk_breakdown': {
                    'equity_risk': self.calculate_equity_risk_score(fund_data),
                    'debt_risk': self.calculate_debt_risk_score(fund_data),
                    'other_risk': self.calculate_other_asset_risk_score(fund_data)
                },
                'additional_metrics': additional_metrics,
                'risk_factors': self._identify_risk_factors(fund_data, composite_risk_score)
            }
            
            return risk_analysis
            
        except Exception as e:
            logger.error(f"Error generating risk analysis: {str(e)}")
            return {
                'fund_name': fund_data.get('fund_name', ''),
                'error': str(e),
                'analysis_date': datetime.now()
            }
    
    def _identify_risk_factors(self, fund_data: Dict, risk_score: float) -> List[str]:
        """
        Identify key risk factors for the fund
        
        Args:
            fund_data: Fund data
            risk_score: Composite risk score
            
        Returns:
            List of risk factors
        """
        risk_factors = []
        
        try:
            # Asset allocation risks
            asset_allocation = fund_data.get('asset_allocation', {})
            equity_weight = asset_allocation.get('equity', 0)
            debt_weight = asset_allocation.get('debt', 0)
            
            if equity_weight > 0.8:
                risk_factors.append("High equity concentration (>80%)")
            elif equity_weight > 0.6:
                risk_factors.append("Moderate-high equity concentration (60-80%)")
            
            if debt_weight > 0.9:
                risk_factors.append("Very high debt concentration (>90%)")
            
            # Market cap risks
            market_cap_exposure = fund_data.get('market_cap_exposure', {})
            small_cap_weight = market_cap_exposure.get('Small Cap', 0)
            mid_cap_weight = market_cap_exposure.get('Mid Cap', 0)
            
            if small_cap_weight > 0.3:
                risk_factors.append("High small-cap exposure (>30%)")
            if mid_cap_weight > 0.5:
                risk_factors.append("High mid-cap exposure (>50%)")
            
            # Sector concentration risks
            sector_exposure = fund_data.get('sector_exposure', {})
            max_sector_weight = max(sector_exposure.values()) if sector_exposure else 0
            
            if max_sector_weight > 0.3:
                risk_factors.append(f"High sector concentration ({max_sector_weight:.1%})")
            
            # Overall risk level
            if risk_score > 80:
                risk_factors.append("Very high overall risk profile")
            elif risk_score > 60:
                risk_factors.append("High overall risk profile")
            elif risk_score < 20:
                risk_factors.append("Very low risk profile - may limit return potential")
            
            return risk_factors
            
        except Exception as e:
            logger.error(f"Error identifying risk factors: {str(e)}")
            return ["Risk factor analysis unavailable"]
    
    def compare_funds_risk(self, funds_data: List[Dict]) -> pd.DataFrame:
        """
        Compare risk profiles of multiple funds
        
        Args:
            funds_data: List of fund data dictionaries
            
        Returns:
            DataFrame with risk comparison
        """
        try:
            comparison_data = []
            
            for fund in funds_data:
                risk_analysis = self.generate_risk_analysis(fund)
                
                comparison_data.append({
                    'Fund Name': fund.get('fund_name', ''),
                    'Category': fund.get('category', ''),
                    'Risk Score': risk_analysis.get('composite_risk_score', 0),
                    'Risk Level': risk_analysis.get('riskometer_level', ''),
                    'Equity %': fund.get('asset_allocation', {}).get('equity', 0) * 100,
                    'Debt %': fund.get('asset_allocation', {}).get('debt', 0) * 100,
                    'Small Cap %': fund.get('market_cap_exposure', {}).get('Small Cap', 0) * 100,
                    'Top Risk Factors': ', '.join(risk_analysis.get('risk_factors', [])[:2])
                })
            
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df = comparison_df.sort_values('Risk Score', ascending=False)
            
            return comparison_df
            
        except Exception as e:
            logger.error(f"Error comparing funds risk: {str(e)}")
            return pd.DataFrame()


# Global analyzer instance
mf_risk_analyzer = MutualFundRiskAnalyzer() 