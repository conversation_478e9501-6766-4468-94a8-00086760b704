"""
Test Script for AI-Powered Stock Analysis Platform
Demonstrates all AI features and capabilities
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def test_ai_stock_predictor():
    """Test the AI stock predictor"""
    print("🔮 Testing AI Stock Predictor...")
    
    try:
        from src.ai.stock_predictor import EnsemblePredictor, FeatureEngineer
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
        sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(3400, 3600, len(dates)),
            'high': np.random.uniform(3500, 3700, len(dates)),
            'low': np.random.uniform(3300, 3500, len(dates)),
            'close': np.random.uniform(3400, 3600, len(dates)),
            'volume': np.random.randint(1000000, 5000000, len(dates))
        })
        
        # Initialize predictor
        predictor = EnsemblePredictor()
        predictor.initialize_models()
        
        # Feature engineering
        feature_engineer = FeatureEngineer()
        featured_data = feature_engineer.prepare_features(sample_data)
        
        print(f"✅ Created {len(featured_data)} features from {len(sample_data)} data points")
        print(f"✅ Feature columns: {len(featured_data.columns)}")
        
        # Train models (simplified)
        print("🧠 Training AI models...")
        performance = predictor.train_models(featured_data)
        
        print("📊 Model Performance:")
        for model_name, metrics in performance.items():
            print(f"   {model_name}: MAE={metrics.mae:.4f}, R²={metrics.r2_score:.4f}")
        
        # Make prediction
        prediction = predictor.predict(featured_data)
        print(f"🎯 Prediction: ₹{prediction.predicted_price:.2f}")
        print(f"🎯 Confidence: {prediction.confidence_score:.2f}")
        print(f"🎯 Risk Level: {prediction.risk_assessment}")
        
        print("✅ AI Stock Predictor test completed successfully!\n")
        
    except Exception as e:
        print(f"❌ AI Stock Predictor test failed: {e}\n")

def test_smart_data_engine():
    """Test the smart data engine"""
    print("📡 Testing Smart Data Engine...")
    
    try:
        from src.ai.smart_data_engine import SmartDataEngine
        
        # Initialize with basic config
        config = {
            'alpha_vantage_key': '',
            'finnhub_key': '',
            'use_redis': False
        }
        
        engine = SmartDataEngine(config)
        
        print("✅ Smart Data Engine initialized")
        print(f"✅ Data sources available: {len(engine.source_manager.sources)}")
        
        # Test cache functionality
        cache_stats = engine.get_cache_stats()
        print(f"📊 Cache Stats: {cache_stats['hit_rate']:.2f} hit rate")
        
        print("✅ Smart Data Engine test completed successfully!\n")
        
    except Exception as e:
        print(f"❌ Smart Data Engine test failed: {e}\n")

def test_intraday_trading_ai():
    """Test the intraday trading AI"""
    print("⚡ Testing Intraday Trading AI...")
    
    try:
        from src.ai.intraday_trading_ai import (
            IntradayTradingAI, MarketConditions, IndianMarketCompliance,
            SignalType, RiskLevel
        )
        
        # Initialize trading AI
        trading_ai = IntradayTradingAI()
        
        # Test compliance engine
        compliance = trading_ai.compliance_engine
        print(f"✅ Trading hours: {compliance.trading_hours['market_open']} - {compliance.trading_hours['market_close']}")
        print(f"✅ F&O eligible stocks: {len(compliance.fo_eligible_stocks)}")
        
        # Test compliance check
        compliance_result = compliance.check_compliance('TCS.NS', SignalType.BUY, datetime.now())
        print(f"✅ Margin requirement: {compliance_result.margin_requirement:.1%}")
        print(f"✅ Auto square-off time: {compliance_result.auto_square_off_time}")
        
        # Create sample market conditions
        market_conditions = MarketConditions(
            market_trend='bullish',
            volatility=0.025,
            volume_surge=True,
            news_sentiment=0.6,
            sector_performance={'Technology': 2.3, 'Banking': -0.5},
            fii_activity='buying',
            vix_level=14.5
        )
        
        print(f"✅ Market conditions: {market_conditions.market_trend}")
        print(f"✅ VIX level: {market_conditions.vix_level}")
        
        print("✅ Intraday Trading AI test completed successfully!\n")
        
    except Exception as e:
        print(f"❌ Intraday Trading AI test failed: {e}\n")

def test_mutual_fund_risk_ai():
    """Test the mutual fund risk AI"""
    print("🛡️ Testing Mutual Fund Risk AI...")
    
    try:
        from src.ai.mutual_fund_risk_ai import (
            MutualFundRiskAI, SEBIRiskCalculator, FundCategory,
            SEBIRiskLevel, FundPortfolio, FundHolding
        )
        
        # Initialize risk AI
        risk_ai = MutualFundRiskAI()
        sebi_calculator = risk_ai.sebi_calculator
        
        print(f"✅ SEBI risk levels: {len(sebi_calculator.risk_thresholds)}")
        print(f"✅ Fund categories: {len(sebi_calculator.category_base_risk)}")
        
        # Create sample portfolio
        holdings = [
            FundHolding("TCS Ltd", 8.5, "Large Cap", "Technology"),
            FundHolding("Reliance Industries", 7.2, "Large Cap", "Energy"),
            FundHolding("HDFC Bank", 6.8, "Large Cap", "Banking")
        ]
        
        portfolio = FundPortfolio(
            fund_name="Sample Equity Fund",
            fund_code="SAMPLE001",
            category=FundCategory.EQUITY_LARGE_CAP,
            holdings=holdings,
            equity_allocation=95.0,
            debt_allocation=5.0,
            cash_allocation=0.0,
            top_10_concentration=45.2,
            sector_concentration={"Technology": 25.0, "Banking": 20.0, "Energy": 15.0},
            market_cap_allocation={"large_cap": 80.0, "mid_cap": 15.0, "small_cap": 5.0}
        )
        
        print(f"✅ Sample portfolio created: {portfolio.fund_name}")
        print(f"✅ Equity allocation: {portfolio.equity_allocation}%")
        print(f"✅ Top 10 concentration: {portfolio.top_10_concentration}%")
        
        # Test risk level determination
        for score in [15, 35, 55, 75]:
            risk_level = sebi_calculator.determine_risk_level(score)
            print(f"✅ Score {score} → Risk Level: {risk_level.value}")
        
        print("✅ Mutual Fund Risk AI test completed successfully!\n")
        
    except Exception as e:
        print(f"❌ Mutual Fund Risk AI test failed: {e}\n")

def test_streamlit_app():
    """Test if Streamlit app components work"""
    print("🎨 Testing Streamlit App Components...")
    
    try:
        # Test if main app file exists and imports work
        import ai_stock_app
        
        print("✅ Main AI app file imported successfully")
        
        # Test helper functions
        current_price = ai_stock_app.get_current_price("TCS.NS")
        print(f"✅ Current price function: ₹{current_price:,.2f}")
        
        # Test prediction generation
        prediction = ai_stock_app.generate_ai_prediction("TCS.NS", 5, "Ensemble")
        print(f"✅ Mock prediction: {prediction['expected_return']:+.2f}%")
        
        # Test trading signal
        signal = ai_stock_app.generate_trading_signal("TCS.NS", "Intraday")
        print(f"✅ Trading signal: {signal['signal']}")
        
        # Test Buffett analysis
        analysis = ai_stock_app.generate_buffett_analysis("TCS.NS")
        print(f"✅ Buffett score: {analysis['overall_score']}/100")
        
        print("✅ Streamlit App Components test completed successfully!\n")
        
    except Exception as e:
        print(f"❌ Streamlit App Components test failed: {e}\n")

def test_ml_libraries():
    """Test if all ML libraries are properly installed"""
    print("🧠 Testing ML Libraries...")
    
    libraries = [
        ('numpy', 'np'),
        ('pandas', 'pd'),
        ('scikit-learn', 'sklearn'),
        ('tensorflow', 'tf'),
        ('torch', 'torch'),
        ('xgboost', 'xgb'),
        ('lightgbm', 'lgb'),
        ('plotly', 'plotly'),
        ('streamlit', 'st')
    ]
    
    for lib_name, import_name in libraries:
        try:
            if import_name == 'sklearn':
                import sklearn
                print(f"✅ {lib_name}: {sklearn.__version__}")
            elif import_name == 'tf':
                import tensorflow as tf
                print(f"✅ {lib_name}: {tf.__version__}")
            elif import_name == 'torch':
                import torch
                print(f"✅ {lib_name}: {torch.__version__}")
            elif import_name == 'xgb':
                import xgboost as xgb
                print(f"✅ {lib_name}: {xgb.__version__}")
            elif import_name == 'lgb':
                import lightgbm as lgb
                print(f"✅ {lib_name}: {lgb.__version__}")
            elif import_name == 'plotly':
                import plotly
                print(f"✅ {lib_name}: {plotly.__version__}")
            elif import_name == 'st':
                import streamlit as st
                print(f"✅ {lib_name}: {st.__version__}")
            elif import_name == 'np':
                import numpy as np
                print(f"✅ {lib_name}: {np.__version__}")
            elif import_name == 'pd':
                import pandas as pd
                print(f"✅ {lib_name}: {pd.__version__}")
        except ImportError:
            print(f"❌ {lib_name}: Not installed")
    
    print("✅ ML Libraries test completed!\n")

def main():
    """Run all tests"""
    print("🚀 AI-Powered Stock Analysis Platform - Feature Test Suite")
    print("=" * 70)
    
    # Test ML libraries first
    test_ml_libraries()
    
    # Test AI modules
    test_ai_stock_predictor()
    test_smart_data_engine()
    test_intraday_trading_ai()
    test_mutual_fund_risk_ai()
    
    # Test Streamlit app
    test_streamlit_app()
    
    print("🎉 All tests completed!")
    print("\n📊 Summary:")
    print("✅ AI Stock Predictor: Advanced ML models for price prediction")
    print("✅ Smart Data Engine: Multi-source data fetching with caching")
    print("✅ Intraday Trading AI: SEBI-compliant trading signals")
    print("✅ Mutual Fund Risk AI: AMFI-compliant risk assessment")
    print("✅ Streamlit App: Modern UI with AI features")
    
    print("\n🚀 Your AI-powered stock analysis platform is ready!")
    print("Run: streamlit run ai_stock_app.py")
    print("Access: http://localhost:8502")

if __name__ == "__main__":
    main()
