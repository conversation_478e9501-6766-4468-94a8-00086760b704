"""
Technical indicators calculation module
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional, Dict, Any
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class IndicatorParams:
    """Parameters for technical indicators"""
    rsi_period: int = 14
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    sma_short: int = 20
    sma_long: int = 50
    ema_short: int = 12
    ema_long: int = 26
    atr_period: int = 14
    bb_period: int = 20
    bb_std: float = 2.0
    vwap_period: int = 20


class TechnicalIndicators:
    """Technical indicators calculator"""
    
    def __init__(self, params: Optional[IndicatorParams] = None):
        self.params = params or IndicatorParams()
    
    def calculate_rsi(self, prices: pd.Series, period: Optional[int] = None) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI)
        
        Args:
            prices: Price series (usually close prices)
            period: RSI period (default: 14)
            
        Returns:
            RSI series
        """
        period = period or self.params.rsi_period
        
        try:
            # Calculate price changes
            delta = prices.diff()
            
            # Separate gains and losses
            gains = delta.where(delta > 0, 0)
            losses = -delta.where(delta < 0, 0)
            
            # Calculate average gains and losses
            avg_gains = gains.rolling(window=period).mean()
            avg_losses = losses.rolling(window=period).mean()
            
            # Calculate RS and RSI
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            logger.error(f"Error calculating RSI: {str(e)}")
            return pd.Series(index=prices.index)
    
    def calculate_macd(self, prices: pd.Series, fast: Optional[int] = None, 
                      slow: Optional[int] = None, signal: Optional[int] = None) -> Dict[str, pd.Series]:
        """
        Calculate MACD (Moving Average Convergence Divergence)
        
        Args:
            prices: Price series
            fast: Fast EMA period
            slow: Slow EMA period
            signal: Signal line period
            
        Returns:
            Dictionary with MACD, signal, and histogram
        """
        fast = fast or self.params.macd_fast
        slow = slow or self.params.macd_slow
        signal_period = signal or self.params.macd_signal
        
        try:
            # Calculate EMAs
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            
            # Calculate MACD line
            macd_line = ema_fast - ema_slow
            
            # Calculate signal line
            signal_line = macd_line.ewm(span=signal_period).mean()
            
            # Calculate histogram
            histogram = macd_line - signal_line
            
            return {
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }
            
        except Exception as e:
            logger.error(f"Error calculating MACD: {str(e)}")
            return {
                'macd': pd.Series(index=prices.index),
                'signal': pd.Series(index=prices.index),
                'histogram': pd.Series(index=prices.index)
            }
    
    def calculate_moving_averages(self, prices: pd.Series, periods: list = None) -> Dict[str, pd.Series]:
        """
        Calculate Simple Moving Averages (SMA)
        
        Args:
            prices: Price series
            periods: List of periods for SMAs
            
        Returns:
            Dictionary with SMA series
        """
        if periods is None:
            periods = [self.params.sma_short, self.params.sma_long]
        
        smas = {}
        
        try:
            for period in periods:
                smas[f'sma_{period}'] = prices.rolling(window=period).mean()
            
            return smas
            
        except Exception as e:
            logger.error(f"Error calculating SMAs: {str(e)}")
            return {f'sma_{period}': pd.Series(index=prices.index) for period in periods}
    
    def calculate_exponential_moving_averages(self, prices: pd.Series, periods: list = None) -> Dict[str, pd.Series]:
        """
        Calculate Exponential Moving Averages (EMA)
        
        Args:
            prices: Price series
            periods: List of periods for EMAs
            
        Returns:
            Dictionary with EMA series
        """
        if periods is None:
            periods = [self.params.ema_short, self.params.ema_long]
        
        emas = {}
        
        try:
            for period in periods:
                emas[f'ema_{period}'] = prices.ewm(span=period).mean()
            
            return emas
            
        except Exception as e:
            logger.error(f"Error calculating EMAs: {str(e)}")
            return {f'ema_{period}': pd.Series(index=prices.index) for period in periods}
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                     period: Optional[int] = None) -> pd.Series:
        """
        Calculate Average True Range (ATR)
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            period: ATR period
            
        Returns:
            ATR series
        """
        period = period or self.params.atr_period
        
        try:
            # Calculate True Range
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            
            # True Range is the maximum of the three
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # Calculate ATR as rolling average of True Range
            atr = true_range.rolling(window=period).mean()
            
            return atr
            
        except Exception as e:
            logger.error(f"Error calculating ATR: {str(e)}")
            return pd.Series(index=high.index)
    
    def calculate_bollinger_bands(self, prices: pd.Series, period: Optional[int] = None, 
                                std_dev: Optional[float] = None) -> Dict[str, pd.Series]:
        """
        Calculate Bollinger Bands
        
        Args:
            prices: Price series
            period: Period for moving average
            std_dev: Standard deviation multiplier
            
        Returns:
            Dictionary with upper, middle, and lower bands
        """
        period = period or self.params.bb_period
        std_dev = std_dev or self.params.bb_std
        
        try:
            # Calculate middle band (SMA)
            middle_band = prices.rolling(window=period).mean()
            
            # Calculate standard deviation
            std = prices.rolling(window=period).std()
            
            # Calculate upper and lower bands
            upper_band = middle_band + (std * std_dev)
            lower_band = middle_band - (std * std_dev)
            
            return {
                'upper': upper_band,
                'middle': middle_band,
                'lower': lower_band
            }
            
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {str(e)}")
            return {
                'upper': pd.Series(index=prices.index),
                'middle': pd.Series(index=prices.index),
                'lower': pd.Series(index=prices.index)
            }
    
    def calculate_vwap(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                      volume: pd.Series, period: Optional[int] = None) -> pd.Series:
        """
        Calculate Volume Weighted Average Price (VWAP)
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            volume: Volume series
            period: Period for VWAP calculation
            
        Returns:
            VWAP series
        """
        period = period or self.params.vwap_period
        
        try:
            # Calculate typical price
            typical_price = (high + low + close) / 3
            
            # Calculate volume-weighted price
            vwap = (typical_price * volume).rolling(window=period).sum() / volume.rolling(window=period).sum()
            
            return vwap
            
        except Exception as e:
            logger.error(f"Error calculating VWAP: {str(e)}")
            return pd.Series(index=high.index)
    
    def calculate_support_resistance(self, high: pd.Series, low: pd.Series, 
                                   close: pd.Series, period: int = 20) -> Dict[str, pd.Series]:
        """
        Calculate support and resistance levels
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            period: Period for calculation
            
        Returns:
            Dictionary with support and resistance levels
        """
        try:
            # Calculate rolling highs and lows
            resistance = high.rolling(window=period).max()
            support = low.rolling(window=period).min()
            
            return {
                'resistance': resistance,
                'support': support
            }
            
        except Exception as e:
            logger.error(f"Error calculating support/resistance: {str(e)}")
            return {
                'resistance': pd.Series(index=high.index),
                'support': pd.Series(index=low.index)
            }
    
    def calculate_volume_indicators(self, close: pd.Series, volume: pd.Series, 
                                  period: int = 20) -> Dict[str, pd.Series]:
        """
        Calculate volume-based indicators
        
        Args:
            close: Close price series
            volume: Volume series
            period: Period for calculation
            
        Returns:
            Dictionary with volume indicators
        """
        try:
            # Volume SMA
            volume_sma = volume.rolling(window=period).mean()
            
            # Volume ratio (current volume / average volume)
            volume_ratio = volume / volume_sma
            
            # Price-volume trend
            pvt = (close.pct_change() * volume).cumsum()
            
            # On-balance volume
            obv = (np.sign(close.diff()) * volume).cumsum()
            
            return {
                'volume_sma': volume_sma,
                'volume_ratio': volume_ratio,
                'pvt': pvt,
                'obv': obv
            }
            
        except Exception as e:
            logger.error(f"Error calculating volume indicators: {str(e)}")
            return {
                'volume_sma': pd.Series(index=close.index),
                'volume_ratio': pd.Series(index=close.index),
                'pvt': pd.Series(index=close.index),
                'obv': pd.Series(index=close.index)
            }
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all technical indicators for a dataset
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with all indicators added
        """
        try:
            result = data.copy()
            
            # Ensure required columns exist
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            for col in required_cols:
                if col not in result.columns:
                    logger.error(f"Required column '{col}' not found in data")
                    return result
            
            # Calculate indicators
            close = result['close']
            high = result['high']
            low = result['low']
            volume = result['volume']
            
            # RSI
            result['rsi'] = self.calculate_rsi(close)
            
            # MACD
            macd_data = self.calculate_macd(close)
            result['macd'] = macd_data['macd']
            result['macd_signal'] = macd_data['signal']
            result['macd_histogram'] = macd_data['histogram']
            
            # Moving Averages
            smas = self.calculate_moving_averages(close)
            for name, sma in smas.items():
                result[name] = sma
            
            emas = self.calculate_exponential_moving_averages(close)
            for name, ema in emas.items():
                result[name] = ema
            
            # ATR
            result['atr'] = self.calculate_atr(high, low, close)
            
            # Bollinger Bands
            bb_data = self.calculate_bollinger_bands(close)
            result['bb_upper'] = bb_data['upper']
            result['bb_middle'] = bb_data['middle']
            result['bb_lower'] = bb_data['lower']
            
            # VWAP
            result['vwap'] = self.calculate_vwap(high, low, close, volume)
            
            # Support and Resistance
            sr_data = self.calculate_support_resistance(high, low, close)
            result['resistance'] = sr_data['resistance']
            result['support'] = sr_data['support']
            
            # Volume Indicators
            volume_data = self.calculate_volume_indicators(close, volume)
            for name, indicator in volume_data.items():
                result[name] = indicator
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating all indicators: {str(e)}")
            return data
    
    def generate_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate trading signals based on technical indicators
        
        Args:
            data: DataFrame with technical indicators
            
        Returns:
            Dictionary with trading signals and analysis
        """
        try:
            signals = {
                'rsi_signals': [],
                'macd_signals': [],
                'ma_signals': [],
                'volume_signals': [],
                'overall_signal': 'hold',
                'confidence': 0.0
            }
            
            if data.empty or len(data) < 50:
                return signals
            
            latest = data.iloc[-1]
            prev = data.iloc[-2]
            
            # RSI signals
            if latest['rsi'] < 30:
                signals['rsi_signals'].append('oversold_buy')
            elif latest['rsi'] > 70:
                signals['rsi_signals'].append('overbought_sell')
            
            # MACD signals
            if (latest['macd'] > latest['macd_signal'] and 
                prev['macd'] <= prev['macd_signal']):
                signals['macd_signals'].append('bullish_crossover')
            elif (latest['macd'] < latest['macd_signal'] and 
                  prev['macd'] >= prev['macd_signal']):
                signals['macd_signals'].append('bearish_crossover')
            
            # Moving Average signals
            if latest['close'] > latest['sma_20'] > latest['sma_50']:
                signals['ma_signals'].append('uptrend')
            elif latest['close'] < latest['sma_20'] < latest['sma_50']:
                signals['ma_signals'].append('downtrend')
            
            # Volume signals
            if latest['volume_ratio'] > 1.5:
                signals['volume_signals'].append('high_volume')
            
            # Overall signal logic
            buy_signals = len([s for s in signals['rsi_signals'] if 'buy' in s])
            sell_signals = len([s for s in signals['rsi_signals'] if 'sell' in s])
            
            if buy_signals > sell_signals and 'bullish_crossover' in signals['macd_signals']:
                signals['overall_signal'] = 'buy'
                signals['confidence'] = 0.7
            elif sell_signals > buy_signals and 'bearish_crossover' in signals['macd_signals']:
                signals['overall_signal'] = 'sell'
                signals['confidence'] = 0.7
            else:
                signals['overall_signal'] = 'hold'
                signals['confidence'] = 0.3
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating signals: {str(e)}")
            return {
                'rsi_signals': [],
                'macd_signals': [],
                'ma_signals': [],
                'volume_signals': [],
                'overall_signal': 'hold',
                'confidence': 0.0
            } 