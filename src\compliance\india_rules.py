"""
India-specific trading compliance rules and regulations
"""

import pandas as pd
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass

from ..data.models import ComplianceRules, Position, PositionSide

logger = logging.getLogger(__name__)


@dataclass
class BrokerConfig:
    """Broker-specific configuration"""
    name: str
    auto_square_off_time: time = time(15, 15)  # 3:15 PM
    margin_requirement: float = 0.20  # 20% minimum
    max_leverage: float = 5.0
    short_selling_allowed: bool = True
    slb_available: bool = True


class IndiaComplianceEngine:
    """India-specific compliance engine for trading rules"""
    
    def __init__(self, broker_config: Optional[BrokerConfig] = None):
        self.broker_config = broker_config or BrokerConfig("Default")
        
        # SEBI short-selling eligible stocks (F&O universe)
        self.short_selling_eligible = {
            'RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'ICICIBANK.NS',
            'HINDUNILVR.NS', 'ITC.NS', 'SBIN.NS', 'BHARTIARTL.NS', 'AXISBANK.NS',
            'KOTAKBANK.NS', 'ASIANPAINT.NS', 'MARUTI.NS', 'HCLTECH.NS', 'SUNPHARMA.NS',
            'TATAMOTORS.NS', 'WIPRO.NS', 'ULTRACEMCO.NS', 'TITAN.NS', 'BAJFINANCE.NS',
            'NESTLEIND.NS', 'POWERGRID.NS', 'BAJAJFINSV.NS', 'NTPC.NS', 'ONGC.NS',
            'TECHM.NS', 'ADANIENT.NS', 'JSWSTEEL.NS', 'TATACONSUM.NS', 'HINDALCO.NS',
            'COALINDIA.NS', 'BRITANNIA.NS', 'DIVISLAB.NS', 'EICHERMOT.NS', 'DRREDDY.NS',
            'SHREECEM.NS', 'CIPLA.NS', 'UPL.NS', 'BPCL.NS', 'HEROMOTOCO.NS',
            'INDUSINDBK.NS', 'TATASTEEL.NS', 'MM.NS', 'BAJAJ-AUTO.NS', 'GRASIM.NS',
            'APOLLOHOSP.NS', 'SBILIFE.NS', 'HDFCLIFE.NS', 'TATAPOWER.NS', 'ADANIPORTS.NS'
        }
        
        # Circuit limit stocks (example)
        self.circuit_limits = {
            'RELIANCE.NS': 0.20,  # 20% circuit limit
            'TCS.NS': 0.20,
            'HDFCBANK.NS': 0.20,
            'INFY.NS': 0.20,
            'ICICIBANK.NS': 0.20
        }
        
        # Market hours
        self.market_open = time(9, 15)  # 9:15 AM
        self.market_close = time(15, 30)  # 3:30 PM
        self.pre_market = time(9, 0)  # 9:00 AM
        self.post_market = time(16, 0)  # 4:00 PM
    
    def check_short_selling_eligibility(self, symbol: str) -> Tuple[bool, str]:
        """
        Check if a stock is eligible for short selling
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Tuple of (is_eligible, reason)
        """
        try:
            # Check if stock is in F&O universe
            if symbol in self.short_selling_eligible:
                return True, "Stock is eligible for short selling (F&O universe)"
            else:
                return False, "Stock not in F&O universe - short selling not allowed"
                
        except Exception as e:
            logger.error(f"Error checking short selling eligibility: {str(e)}")
            return False, f"Error: {str(e)}"
    
    def check_margin_requirements(self, position_value: float, available_margin: float) -> Tuple[bool, float, str]:
        """
        Check margin requirements for a position
        
        Args:
            position_value: Total value of the position
            available_margin: Available margin in account
            
        Returns:
            Tuple of (is_sufficient, required_margin, message)
        """
        try:
            # SEBI peak margin regime - minimum 20% upfront margin
            required_margin = position_value * self.broker_config.margin_requirement
            
            if available_margin >= required_margin:
                return True, required_margin, f"Margin sufficient. Required: ₹{required_margin:,.2f}"
            else:
                shortfall = required_margin - available_margin
                return False, required_margin, f"Insufficient margin. Required: ₹{required_margin:,.2f}, Shortfall: ₹{shortfall:,.2f}"
                
        except Exception as e:
            logger.error(f"Error checking margin requirements: {str(e)}")
            return False, 0, f"Error: {str(e)}"
    
    def calculate_leverage(self, position_value: float, margin_used: float) -> float:
        """
        Calculate effective leverage for a position
        
        Args:
            position_value: Total value of the position
            margin_used: Margin used for the position
            
        Returns:
            Effective leverage ratio
        """
        try:
            if margin_used > 0:
                return position_value / margin_used
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating leverage: {str(e)}")
            return 0.0
    
    def check_circuit_limits(self, symbol: str, current_price: float, 
                            previous_close: float) -> Tuple[bool, float, str]:
        """
        Check if stock is hitting circuit limits
        
        Args:
            symbol: Stock symbol
            current_price: Current price
            previous_close: Previous day's close price
            
        Returns:
            Tuple of (within_limits, limit_percentage, message)
        """
        try:
            if symbol not in self.circuit_limits:
                return True, 0.0, "No circuit limit information available"
            
            circuit_limit = self.circuit_limits[symbol]
            price_change = abs(current_price - previous_close) / previous_close
            
            if price_change >= circuit_limit:
                return False, circuit_limit, f"Circuit limit hit! Change: {price_change:.2%}, Limit: {circuit_limit:.2%}"
            else:
                remaining_limit = circuit_limit - price_change
                return True, circuit_limit, f"Within limits. Remaining: {remaining_limit:.2%}"
                
        except Exception as e:
            logger.error(f"Error checking circuit limits: {str(e)}")
            return True, 0.0, f"Error: {str(e)}"
    
    def check_auto_square_off_timing(self, current_time: time) -> Tuple[bool, str]:
        """
        Check if auto square-off is approaching
        
        Args:
            current_time: Current time
            
        Returns:
            Tuple of (should_warn, message)
        """
        try:
            auto_square_off = self.broker_config.auto_square_off_time
            
            # Calculate time difference
            current_minutes = current_time.hour * 60 + current_time.minute
            auto_square_minutes = auto_square_off.hour * 60 + auto_square_off.minute
            
            time_diff = auto_square_minutes - current_minutes
            
            if time_diff <= 0:
                return True, "Auto square-off time has passed!"
            elif time_diff <= 15:
                return True, f"Auto square-off in {time_diff} minutes!"
            elif time_diff <= 30:
                return True, f"Auto square-off in {time_diff} minutes"
            else:
                return False, f"Auto square-off in {time_diff} minutes"
                
        except Exception as e:
            logger.error(f"Error checking auto square-off timing: {str(e)}")
            return False, f"Error: {str(e)}"
    
    def validate_intraday_position(self, position: Position, current_time: time) -> Dict[str, Any]:
        """
        Validate an intraday position for compliance
        
        Args:
            position: Position object
            current_time: Current time
            
        Returns:
            Dictionary with validation results
        """
        try:
            validation = {
                'is_valid': True,
                'warnings': [],
                'errors': [],
                'actions_required': []
            }
            
            # Check short selling eligibility for short positions
            if position.side == PositionSide.SHORT:
                is_eligible, reason = self.check_short_selling_eligibility(position.symbol)
                if not is_eligible:
                    validation['is_valid'] = False
                    validation['errors'].append(f"Short selling not allowed: {reason}")
            
            # Check margin requirements
            margin_sufficient, required_margin, margin_msg = self.check_margin_requirements(
                position.current_price * position.quantity, 
                position.margin_used
            )
            
            if not margin_sufficient:
                validation['warnings'].append(margin_msg)
                validation['actions_required'].append("Add more margin or reduce position size")
            
            # Check leverage
            leverage = self.calculate_leverage(
                position.current_price * position.quantity, 
                position.margin_used
            )
            
            if leverage > self.broker_config.max_leverage:
                validation['warnings'].append(f"High leverage: {leverage:.2f}x (Max: {self.broker_config.max_leverage:.2f}x)")
            
            # Check auto square-off timing
            should_warn, timing_msg = self.check_auto_square_off_timing(current_time)
            if should_warn:
                validation['warnings'].append(timing_msg)
                if "passed" in timing_msg:
                    validation['actions_required'].append("Close position immediately")
            
            # Check if position can be held overnight (for short positions)
            if position.side == PositionSide.SHORT:
                if current_time >= self.market_close:
                    validation['errors'].append("Short positions cannot be held overnight in cash market")
                    validation['actions_required'].append("Square off short position before market close")
            
            return validation
            
        except Exception as e:
            logger.error(f"Error validating position: {str(e)}")
            return {
                'is_valid': False,
                'warnings': [],
                'errors': [f"Validation error: {str(e)}"],
                'actions_required': ["Contact support"]
            }
    
    def get_compliance_rules(self, symbol: str) -> ComplianceRules:
        """
        Get compliance rules for a specific stock
        
        Args:
            symbol: Stock symbol
            
        Returns:
            ComplianceRules object
        """
        try:
            is_short_allowed, _ = self.check_short_selling_eligibility(symbol)
            
            # Get circuit limit
            circuit_limit = self.circuit_limits.get(symbol, 0.20)  # Default 20%
            
            return ComplianceRules(
                short_selling_allowed=is_short_allowed,
                slb_eligible=is_short_allowed,
                margin_requirement=self.broker_config.margin_requirement,
                auto_square_off_time=self.broker_config.auto_square_off_time.strftime("%H:%M"),
                circuit_limit=circuit_limit,
                delivery_obligation=True,  # Always true for cash market
                overnight_short_allowed=False  # Never allowed for retail cash market
            )
            
        except Exception as e:
            logger.error(f"Error getting compliance rules: {str(e)}")
            return ComplianceRules(
                short_selling_allowed=False,
                slb_eligible=False,
                margin_requirement=0.20,
                auto_square_off_time="15:15",
                circuit_limit=0.20,
                delivery_obligation=True,
                overnight_short_allowed=False
            )
    
    def calculate_position_risk(self, position: Position, market_data: Dict) -> Dict[str, Any]:
        """
        Calculate risk metrics for a position
        
        Args:
            position: Position object
            market_data: Market data including volatility, etc.
            
        Returns:
            Dictionary with risk metrics
        """
        try:
            risk_metrics = {
                'position_value': position.current_price * position.quantity,
                'unrealized_pnl': position.pnl,
                'unrealized_pnl_percent': position.pnl_percentage,
                'margin_utilization': (position.margin_used / (position.current_price * position.quantity)) * 100,
                'leverage': self.calculate_leverage(position.current_price * position.quantity, position.margin_used),
                'risk_score': 0.0,
                'risk_level': 'LOW'
            }
            
            # Calculate risk score based on multiple factors
            risk_score = 0
            
            # Leverage risk (0-30 points)
            leverage = risk_metrics['leverage']
            if leverage > 4:
                risk_score += 30
            elif leverage > 3:
                risk_score += 20
            elif leverage > 2:
                risk_score += 10
            
            # P&L risk (0-30 points)
            pnl_percent = abs(position.pnl_percentage)
            if pnl_percent > 10:
                risk_score += 30
            elif pnl_percent > 5:
                risk_score += 20
            elif pnl_percent > 2:
                risk_score += 10
            
            # Margin utilization risk (0-20 points)
            margin_util = risk_metrics['margin_utilization']
            if margin_util > 80:
                risk_score += 20
            elif margin_util > 60:
                risk_score += 15
            elif margin_util > 40:
                risk_score += 10
            
            # Position size risk (0-20 points)
            position_value = risk_metrics['position_value']
            if position_value > 1000000:  # 10 lakhs
                risk_score += 20
            elif position_value > 500000:  # 5 lakhs
                risk_score += 15
            elif position_value > 100000:  # 1 lakh
                risk_score += 10
            
            risk_metrics['risk_score'] = risk_score
            
            # Assign risk level
            if risk_score >= 80:
                risk_metrics['risk_level'] = 'VERY_HIGH'
            elif risk_score >= 60:
                risk_metrics['risk_level'] = 'HIGH'
            elif risk_score >= 40:
                risk_metrics['risk_level'] = 'MODERATE'
            elif risk_score >= 20:
                risk_metrics['risk_level'] = 'LOW'
            else:
                risk_metrics['risk_level'] = 'VERY_LOW'
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error calculating position risk: {str(e)}")
            return {
                'position_value': 0,
                'unrealized_pnl': 0,
                'unrealized_pnl_percent': 0,
                'margin_utilization': 0,
                'leverage': 0,
                'risk_score': 0,
                'risk_level': 'UNKNOWN'
            }


# Global compliance engine instance
compliance_engine = IndiaComplianceEngine() 