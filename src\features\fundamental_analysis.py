"""
Buffett-style fundamental analysis module
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass

from ..data.models import FundamentalData, RiskLevel

logger = logging.getLogger(__name__)


@dataclass
class BuffettCriteria:
    """Buffett-style investment criteria"""
    min_roe: float = 15.0  # Minimum ROE 15%
    max_debt_to_equity: float = 0.5  # Maximum debt-to-equity 0.5
    min_market_cap: float = 10000  # Minimum market cap in crores
    max_pe_ratio: float = 25.0  # Maximum PE ratio
    min_profit_margin: float = 10.0  # Minimum profit margin 10%
    min_revenue_growth: float = 8.0  # Minimum revenue growth 8%
    max_beta: float = 1.2  # Maximum beta for stability


class BuffettAnalyzer:
    """Buffett-style fundamental stock analyzer"""
    
    def __init__(self, criteria: Optional[BuffettCriteria] = None):
        self.criteria = criteria or BuffettCriteria()
        
        # Sector-specific adjustments
        self.sector_adjustments = {
            'Banking': {
                'max_debt_to_equity': 8.0,  # Banks can have higher leverage
                'min_roe': 12.0,  # Lower ROE requirement for banks
                'max_pe_ratio': 20.0
            },
            'Oil & Gas': {
                'max_debt_to_equity': 0.8,
                'min_roe': 12.0,
                'max_pe_ratio': 18.0
            },
            'IT': {
                'max_debt_to_equity': 0.3,  # IT companies should have low debt
                'min_roe': 18.0,  # Higher ROE requirement for IT
                'max_pe_ratio': 30.0
            },
            'FMCG': {
                'max_debt_to_equity': 0.4,
                'min_roe': 20.0,  # High ROE requirement for FMCG
                'max_pe_ratio': 35.0
            }
        }
    
    def calculate_quality_score(self, fundamental_data: Dict) -> Dict[str, Any]:
        """
        Calculate Buffett-style quality score
        
        Args:
            fundamental_data: Dictionary with fundamental metrics
            
        Returns:
            Dictionary with quality score and breakdown
        """
        try:
            quality_metrics = {
                'roe_score': 0.0,
                'debt_score': 0.0,
                'profitability_score': 0.0,
                'growth_score': 0.0,
                'stability_score': 0.0,
                'overall_quality_score': 0.0,
                'quality_grade': 'F'
            }
            
            # Get sector-specific criteria
            sector = fundamental_data.get('sector', 'General')
            sector_criteria = self.sector_adjustments.get(sector, {})
            
            # ROE Score (0-25 points)
            roe = fundamental_data.get('roe', 0)
            min_roe = sector_criteria.get('min_roe', self.criteria.min_roe)
            
            if roe >= min_roe:
                quality_metrics['roe_score'] = 25.0
            elif roe >= min_roe * 0.8:
                quality_metrics['roe_score'] = 20.0
            elif roe >= min_roe * 0.6:
                quality_metrics['roe_score'] = 15.0
            elif roe >= min_roe * 0.4:
                quality_metrics['roe_score'] = 10.0
            else:
                quality_metrics['roe_score'] = 0.0
            
            # Debt Score (0-20 points)
            debt_to_equity = fundamental_data.get('debt_to_equity', 999)
            max_debt = sector_criteria.get('max_debt_to_equity', self.criteria.max_debt_to_equity)
            
            if debt_to_equity <= max_debt:
                quality_metrics['debt_score'] = 20.0
            elif debt_to_equity <= max_debt * 1.5:
                quality_metrics['debt_score'] = 15.0
            elif debt_to_equity <= max_debt * 2.0:
                quality_metrics['debt_score'] = 10.0
            else:
                quality_metrics['debt_score'] = 0.0
            
            # Profitability Score (0-20 points)
            profit_margin = fundamental_data.get('profit_margin', 0)
            min_margin = self.criteria.min_profit_margin
            
            if profit_margin >= min_margin:
                quality_metrics['profitability_score'] = 20.0
            elif profit_margin >= min_margin * 0.8:
                quality_metrics['profitability_score'] = 15.0
            elif profit_margin >= min_margin * 0.6:
                quality_metrics['profitability_score'] = 10.0
            else:
                quality_metrics['profitability_score'] = 0.0
            
            # Growth Score (0-20 points)
            revenue_growth = fundamental_data.get('revenue_growth', 0)
            profit_growth = fundamental_data.get('profit_growth', 0)
            min_growth = self.criteria.min_revenue_growth
            
            growth_score = 0
            if revenue_growth >= min_growth:
                growth_score += 10
            if profit_growth >= min_growth:
                growth_score += 10
            
            quality_metrics['growth_score'] = growth_score
            
            # Stability Score (0-15 points)
            beta = fundamental_data.get('beta', 1.0)
            max_beta = self.criteria.max_beta
            
            if beta <= max_beta:
                quality_metrics['stability_score'] = 15.0
            elif beta <= max_beta * 1.2:
                quality_metrics['stability_score'] = 10.0
            elif beta <= max_beta * 1.5:
                quality_metrics['stability_score'] = 5.0
            else:
                quality_metrics['stability_score'] = 0.0
            
            # Calculate overall quality score
            total_score = sum([
                quality_metrics['roe_score'],
                quality_metrics['debt_score'],
                quality_metrics['profitability_score'],
                quality_metrics['growth_score'],
                quality_metrics['stability_score']
            ])
            
            quality_metrics['overall_quality_score'] = total_score
            
            # Assign quality grade
            if total_score >= 90:
                quality_metrics['quality_grade'] = 'A+'
            elif total_score >= 80:
                quality_metrics['quality_grade'] = 'A'
            elif total_score >= 70:
                quality_metrics['quality_grade'] = 'B+'
            elif total_score >= 60:
                quality_metrics['quality_grade'] = 'B'
            elif total_score >= 50:
                quality_metrics['quality_grade'] = 'C+'
            elif total_score >= 40:
                quality_metrics['quality_grade'] = 'C'
            elif total_score >= 30:
                quality_metrics['quality_grade'] = 'D'
            else:
                quality_metrics['quality_grade'] = 'F'
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"Error calculating quality score: {str(e)}")
            return {
                'roe_score': 0.0,
                'debt_score': 0.0,
                'profitability_score': 0.0,
                'growth_score': 0.0,
                'stability_score': 0.0,
                'overall_quality_score': 0.0,
                'quality_grade': 'F'
            }
    
    def calculate_valuation_score(self, fundamental_data: Dict) -> Dict[str, Any]:
        """
        Calculate Buffett-style valuation score
        
        Args:
            fundamental_data: Dictionary with fundamental metrics
            
        Returns:
            Dictionary with valuation score and breakdown
        """
        try:
            valuation_metrics = {
                'pe_score': 0.0,
                'pb_score': 0.0,
                'dividend_score': 0.0,
                'market_cap_score': 0.0,
                'overall_valuation_score': 0.0,
                'valuation_grade': 'F'
            }
            
            # Get sector-specific criteria
            sector = fundamental_data.get('sector', 'General')
            sector_criteria = self.sector_adjustments.get(sector, {})
            
            # PE Ratio Score (0-30 points)
            pe_ratio = fundamental_data.get('pe_ratio', 999)
            max_pe = sector_criteria.get('max_pe_ratio', self.criteria.max_pe_ratio)
            
            if pe_ratio <= max_pe * 0.7:
                valuation_metrics['pe_score'] = 30.0
            elif pe_ratio <= max_pe * 0.85:
                valuation_metrics['pe_score'] = 25.0
            elif pe_ratio <= max_pe:
                valuation_metrics['pe_score'] = 20.0
            elif pe_ratio <= max_pe * 1.2:
                valuation_metrics['pe_score'] = 15.0
            elif pe_ratio <= max_pe * 1.5:
                valuation_metrics['pe_score'] = 10.0
            else:
                valuation_metrics['pe_score'] = 0.0
            
            # PB Ratio Score (0-25 points)
            pb_ratio = fundamental_data.get('pb_ratio', 999)
            
            if pb_ratio <= 1.0:
                valuation_metrics['pb_score'] = 25.0
            elif pb_ratio <= 1.5:
                valuation_metrics['pb_score'] = 20.0
            elif pb_ratio <= 2.0:
                valuation_metrics['pb_score'] = 15.0
            elif pb_ratio <= 3.0:
                valuation_metrics['pb_score'] = 10.0
            elif pb_ratio <= 5.0:
                valuation_metrics['pb_score'] = 5.0
            else:
                valuation_metrics['pb_score'] = 0.0
            
            # Dividend Score (0-20 points)
            dividend_yield = fundamental_data.get('dividend_yield', 0)
            
            if dividend_yield >= 3.0:
                valuation_metrics['dividend_score'] = 20.0
            elif dividend_yield >= 2.0:
                valuation_metrics['dividend_score'] = 15.0
            elif dividend_yield >= 1.0:
                valuation_metrics['dividend_score'] = 10.0
            elif dividend_yield >= 0.5:
                valuation_metrics['dividend_score'] = 5.0
            else:
                valuation_metrics['dividend_score'] = 0.0
            
            # Market Cap Score (0-25 points)
            market_cap = fundamental_data.get('market_cap', 0) / 10000000  # Convert to crores
            min_market_cap = self.criteria.min_market_cap
            
            if market_cap >= min_market_cap * 2:
                valuation_metrics['market_cap_score'] = 25.0
            elif market_cap >= min_market_cap:
                valuation_metrics['market_cap_score'] = 20.0
            elif market_cap >= min_market_cap * 0.7:
                valuation_metrics['market_cap_score'] = 15.0
            elif market_cap >= min_market_cap * 0.5:
                valuation_metrics['market_cap_score'] = 10.0
            else:
                valuation_metrics['market_cap_score'] = 0.0
            
            # Calculate overall valuation score
            total_score = sum([
                valuation_metrics['pe_score'],
                valuation_metrics['pb_score'],
                valuation_metrics['dividend_score'],
                valuation_metrics['market_cap_score']
            ])
            
            valuation_metrics['overall_valuation_score'] = total_score
            
            # Assign valuation grade
            if total_score >= 80:
                valuation_metrics['valuation_grade'] = 'A+'
            elif total_score >= 70:
                valuation_metrics['valuation_grade'] = 'A'
            elif total_score >= 60:
                valuation_metrics['valuation_grade'] = 'B+'
            elif total_score >= 50:
                valuation_metrics['valuation_grade'] = 'B'
            elif total_score >= 40:
                valuation_metrics['valuation_grade'] = 'C+'
            elif total_score >= 30:
                valuation_metrics['valuation_grade'] = 'C'
            elif total_score >= 20:
                valuation_metrics['valuation_grade'] = 'D'
            else:
                valuation_metrics['valuation_grade'] = 'F'
            
            return valuation_metrics
            
        except Exception as e:
            logger.error(f"Error calculating valuation score: {str(e)}")
            return {
                'pe_score': 0.0,
                'pb_score': 0.0,
                'dividend_score': 0.0,
                'market_cap_score': 0.0,
                'overall_valuation_score': 0.0,
                'valuation_grade': 'F'
            }
    
    def calculate_moat_score(self, fundamental_data: Dict) -> Dict[str, Any]:
        """
        Calculate Buffett-style moat score
        
        Args:
            fundamental_data: Dictionary with fundamental metrics
            
        Returns:
            Dictionary with moat score and breakdown
        """
        try:
            moat_metrics = {
                'roe_consistency_score': 0.0,
                'profit_margin_stability_score': 0.0,
                'market_share_score': 0.0,
                'brand_value_score': 0.0,
                'overall_moat_score': 0.0,
                'moat_grade': 'F'
            }
            
            # ROE Consistency Score (0-30 points)
            roe = fundamental_data.get('roe', 0)
            roe_5y = fundamental_data.get('roe_5y', roe)  # 5-year average ROE
            
            if roe_5y and abs(roe - roe_5y) <= 2.0:
                moat_metrics['roe_consistency_score'] = 30.0
            elif roe_5y and abs(roe - roe_5y) <= 5.0:
                moat_metrics['roe_consistency_score'] = 20.0
            elif roe_5y and abs(roe - roe_5y) <= 10.0:
                moat_metrics['roe_consistency_score'] = 10.0
            else:
                moat_metrics['roe_consistency_score'] = 0.0
            
            # Profit Margin Stability Score (0-25 points)
            profit_margin = fundamental_data.get('profit_margin', 0)
            profit_margin_5y = fundamental_data.get('profit_margin_5y', profit_margin)
            
            if profit_margin_5y and abs(profit_margin - profit_margin_5y) <= 1.0:
                moat_metrics['profit_margin_stability_score'] = 25.0
            elif profit_margin_5y and abs(profit_margin - profit_margin_5y) <= 3.0:
                moat_metrics['profit_margin_stability_score'] = 15.0
            elif profit_margin_5y and abs(profit_margin - profit_margin_5y) <= 5.0:
                moat_metrics['profit_margin_stability_score'] = 10.0
            else:
                moat_metrics['profit_margin_stability_score'] = 0.0
            
            # Market Share Score (0-25 points)
            # This would require additional data, using placeholder logic
            sector = fundamental_data.get('sector', '')
            if sector in ['FMCG', 'Banking', 'IT']:
                moat_metrics['market_share_score'] = 20.0  # Assume established sectors
            else:
                moat_metrics['market_share_score'] = 10.0
            
            # Brand Value Score (0-20 points)
            # This would require brand value data, using sector-based logic
            if sector in ['FMCG', 'Automobile']:
                moat_metrics['brand_value_score'] = 20.0
            elif sector in ['Banking', 'IT']:
                moat_metrics['brand_value_score'] = 15.0
            else:
                moat_metrics['brand_value_score'] = 10.0
            
            # Calculate overall moat score
            total_score = sum([
                moat_metrics['roe_consistency_score'],
                moat_metrics['profit_margin_stability_score'],
                moat_metrics['market_share_score'],
                moat_metrics['brand_value_score']
            ])
            
            moat_metrics['overall_moat_score'] = total_score
            
            # Assign moat grade
            if total_score >= 80:
                moat_metrics['moat_grade'] = 'A+'
            elif total_score >= 70:
                moat_metrics['moat_grade'] = 'A'
            elif total_score >= 60:
                moat_metrics['moat_grade'] = 'B+'
            elif total_score >= 50:
                moat_metrics['moat_grade'] = 'B'
            elif total_score >= 40:
                moat_metrics['moat_grade'] = 'C+'
            elif total_score >= 30:
                moat_metrics['moat_grade'] = 'C'
            elif total_score >= 20:
                moat_metrics['moat_grade'] = 'D'
            else:
                moat_metrics['moat_grade'] = 'F'
            
            return moat_metrics
            
        except Exception as e:
            logger.error(f"Error calculating moat score: {str(e)}")
            return {
                'roe_consistency_score': 0.0,
                'profit_margin_stability_score': 0.0,
                'market_share_score': 0.0,
                'brand_value_score': 0.0,
                'overall_moat_score': 0.0,
                'moat_grade': 'F'
            }
    
    def generate_buffett_analysis(self, fundamental_data: Dict) -> Dict[str, Any]:
        """
        Generate comprehensive Buffett-style analysis
        
        Args:
            fundamental_data: Dictionary with fundamental metrics
            
        Returns:
            Dictionary with complete Buffett analysis
        """
        try:
            # Calculate all scores
            quality_score = self.calculate_quality_score(fundamental_data)
            valuation_score = self.calculate_valuation_score(fundamental_data)
            moat_score = self.calculate_moat_score(fundamental_data)
            
            # Calculate composite score
            composite_score = (
                quality_score['overall_quality_score'] * 0.4 +
                valuation_score['overall_valuation_score'] * 0.35 +
                moat_score['overall_moat_score'] * 0.25
            )
            
            # Assign overall grade
            if composite_score >= 85:
                overall_grade = 'A+'
                recommendation = 'Strong Buy'
                confidence = 'High'
            elif composite_score >= 75:
                overall_grade = 'A'
                recommendation = 'Buy'
                confidence = 'High'
            elif composite_score >= 65:
                overall_grade = 'B+'
                recommendation = 'Buy'
                confidence = 'Medium'
            elif composite_score >= 55:
                overall_grade = 'B'
                recommendation = 'Hold'
                confidence = 'Medium'
            elif composite_score >= 45:
                overall_grade = 'C+'
                recommendation = 'Hold'
                confidence = 'Low'
            elif composite_score >= 35:
                overall_grade = 'C'
                recommendation = 'Sell'
                confidence = 'Medium'
            else:
                overall_grade = 'D/F'
                recommendation = 'Sell'
                confidence = 'High'
            
            # Generate reasoning
            reasoning = self._generate_reasoning(
                quality_score, valuation_score, moat_score, fundamental_data
            )
            
            return {
                'symbol': fundamental_data.get('symbol', ''),
                'sector': fundamental_data.get('sector', ''),
                'composite_score': composite_score,
                'overall_grade': overall_grade,
                'recommendation': recommendation,
                'confidence': confidence,
                'quality_score': quality_score,
                'valuation_score': valuation_score,
                'moat_score': moat_score,
                'reasoning': reasoning,
                'analysis_timestamp': pd.Timestamp.now()
            }
            
        except Exception as e:
            logger.error(f"Error generating Buffett analysis: {str(e)}")
            return {
                'symbol': fundamental_data.get('symbol', ''),
                'error': str(e),
                'analysis_timestamp': pd.Timestamp.now()
            }
    
    def _generate_reasoning(self, quality_score: Dict, valuation_score: Dict, 
                           moat_score: Dict, fundamental_data: Dict) -> List[str]:
        """
        Generate reasoning for the analysis
        
        Args:
            quality_score: Quality score breakdown
            valuation_score: Valuation score breakdown
            moat_score: Moat score breakdown
            fundamental_data: Original fundamental data
            
        Returns:
            List of reasoning points
        """
        reasoning = []
        
        try:
            # Quality reasoning
            if quality_score['roe_score'] >= 20:
                reasoning.append(f"Strong ROE of {fundamental_data.get('roe', 0):.1f}% indicates efficient capital utilization")
            elif quality_score['roe_score'] <= 10:
                reasoning.append(f"Low ROE of {fundamental_data.get('roe', 0):.1f}% suggests poor capital efficiency")
            
            if quality_score['debt_score'] >= 15:
                reasoning.append("Low debt levels indicate strong financial health")
            elif quality_score['debt_score'] <= 5:
                reasoning.append("High debt levels pose financial risk")
            
            # Valuation reasoning
            if valuation_score['pe_score'] >= 20:
                pe_ratio = fundamental_data.get('pe_ratio', 0)
                reasoning.append(f"PE ratio of {pe_ratio:.1f} is within reasonable range")
            elif valuation_score['pe_score'] <= 10:
                pe_ratio = fundamental_data.get('pe_ratio', 0)
                reasoning.append(f"High PE ratio of {pe_ratio:.1f} suggests overvaluation")
            
            if valuation_score['pb_score'] >= 15:
                pb_ratio = fundamental_data.get('pb_ratio', 0)
                reasoning.append(f"PB ratio of {pb_ratio:.1f} indicates good value")
            
            # Moat reasoning
            if moat_score['overall_moat_score'] >= 60:
                reasoning.append("Company shows characteristics of having economic moats")
            elif moat_score['overall_moat_score'] <= 30:
                reasoning.append("Limited evidence of economic moats")
            
            # Sector-specific reasoning
            sector = fundamental_data.get('sector', '')
            if sector == 'Banking':
                reasoning.append("Banking sector requires careful monitoring of asset quality and regulatory environment")
            elif sector == 'IT':
                reasoning.append("IT sector benefits from global demand but faces currency and competition risks")
            elif sector == 'FMCG':
                reasoning.append("FMCG sector offers stable growth and defensive characteristics")
            
            return reasoning
            
        except Exception as e:
            logger.error(f"Error generating reasoning: {str(e)}")
            return ["Analysis completed with some limitations"]


# Global analyzer instance
buffett_analyzer = BuffettAnalyzer() 