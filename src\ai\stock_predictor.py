"""
AI-Powered Stock Price Prediction Engine
Implements multiple ML models for stock price forecasting with ensemble methods
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib

# Deep Learning (will be imported conditionally)
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    HAS_TENSORFLOW = True
except ImportError:
    HAS_TENSORFLOW = False

try:
    import torch
    import torch.nn as nn
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False

# XGBoost and LightGBM
try:
    import xgboost as xgb
    import lightgbm as lgb
    HAS_BOOSTING = True
except ImportError:
    HAS_BOOSTING = False

from dataclasses import dataclass
from loguru import logger

@dataclass
class PredictionResult:
    """Stock price prediction result"""
    symbol: str
    prediction_date: datetime
    predicted_price: float
    confidence_interval: Tuple[float, float]
    confidence_score: float
    model_used: str
    features_importance: Dict[str, float]
    technical_signals: Dict[str, Any]
    risk_assessment: str

@dataclass
class ModelPerformance:
    """Model performance metrics"""
    model_name: str
    mse: float
    mae: float
    r2_score: float
    accuracy_direction: float  # Percentage of correct direction predictions
    sharpe_ratio: float

class FeatureEngineer:
    """Advanced feature engineering for stock prediction"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.price_scaler = MinMaxScaler()
        
    def create_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive technical analysis features"""
        data = df.copy()
        
        # Price-based features
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        data['price_change'] = data['close'] - data['open']
        data['price_range'] = data['high'] - data['low']
        data['body_size'] = abs(data['close'] - data['open'])
        
        # Moving averages
        for period in [5, 10, 20, 50, 200]:
            data[f'sma_{period}'] = data['close'].rolling(period).mean()
            data[f'ema_{period}'] = data['close'].ewm(span=period).mean()
            data[f'price_to_sma_{period}'] = data['close'] / data[f'sma_{period}']
        
        # Volatility features
        data['volatility_5'] = data['returns'].rolling(5).std()
        data['volatility_20'] = data['returns'].rolling(20).std()
        data['atr'] = self._calculate_atr(data)
        
        # Volume features
        data['volume_sma_20'] = data['volume'].rolling(20).mean()
        data['volume_ratio'] = data['volume'] / data['volume_sma_20']
        data['price_volume'] = data['close'] * data['volume']
        
        # Momentum indicators
        data['rsi'] = self._calculate_rsi(data['close'])
        data['macd'], data['macd_signal'] = self._calculate_macd(data['close'])
        data['bb_upper'], data['bb_lower'], data['bb_middle'] = self._calculate_bollinger_bands(data['close'])
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
        
        # Trend features
        data['trend_5'] = data['close'].rolling(5).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0)
        data['trend_20'] = data['close'].rolling(20).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0)
        
        # Support and resistance levels
        data['support'] = data['low'].rolling(20).min()
        data['resistance'] = data['high'].rolling(20).max()
        data['support_distance'] = (data['close'] - data['support']) / data['close']
        data['resistance_distance'] = (data['resistance'] - data['close']) / data['close']
        
        return data
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """Calculate MACD and Signal line"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        return macd, signal_line
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands"""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, lower, sma
    
    def create_lag_features(self, df: pd.DataFrame, target_col: str = 'close', lags: List[int] = [1, 2, 3, 5, 10]) -> pd.DataFrame:
        """Create lagged features for time series prediction"""
        data = df.copy()
        for lag in lags:
            data[f'{target_col}_lag_{lag}'] = data[target_col].shift(lag)
            data[f'returns_lag_{lag}'] = data['returns'].shift(lag)
        return data
    
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare all features for ML models"""
        # Create technical features
        data = self.create_technical_features(df)
        
        # Create lag features
        data = self.create_lag_features(data)
        
        # Drop rows with NaN values
        data = data.dropna()
        
        return data

class LSTMPredictor:
    """LSTM Neural Network for stock price prediction"""
    
    def __init__(self, sequence_length: int = 60, features: int = 1):
        self.sequence_length = sequence_length
        self.features = features
        self.model = None
        self.scaler = MinMaxScaler()
        
    def build_model(self) -> None:
        """Build LSTM model architecture"""
        if not HAS_TENSORFLOW:
            raise ImportError("TensorFlow not available for LSTM model")
            
        self.model = keras.Sequential([
            layers.LSTM(50, return_sequences=True, input_shape=(self.sequence_length, self.features)),
            layers.Dropout(0.2),
            layers.LSTM(50, return_sequences=True),
            layers.Dropout(0.2),
            layers.LSTM(50),
            layers.Dropout(0.2),
            layers.Dense(25),
            layers.Dense(1)
        ])
        
        self.model.compile(optimizer='adam', loss='mean_squared_error', metrics=['mae'])
    
    def prepare_sequences(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for LSTM training"""
        X, y = [], []
        for i in range(self.sequence_length, len(data)):
            X.append(data[i-self.sequence_length:i])
            y.append(data[i])
        return np.array(X), np.array(y)
    
    def train(self, data: pd.DataFrame, target_col: str = 'close') -> Dict[str, float]:
        """Train LSTM model"""
        if self.model is None:
            self.build_model()
            
        # Scale data
        scaled_data = self.scaler.fit_transform(data[[target_col]])
        
        # Prepare sequences
        X, y = self.prepare_sequences(scaled_data)
        
        # Split data
        split_idx = int(0.8 * len(X))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Train model
        history = self.model.fit(
            X_train, y_train,
            epochs=50,
            batch_size=32,
            validation_data=(X_test, y_test),
            verbose=0
        )
        
        # Evaluate
        train_loss = self.model.evaluate(X_train, y_train, verbose=0)
        test_loss = self.model.evaluate(X_test, y_test, verbose=0)
        
        return {
            'train_loss': train_loss[0],
            'test_loss': test_loss[0],
            'train_mae': train_loss[1],
            'test_mae': test_loss[1]
        }
    
    def predict(self, data: pd.DataFrame, target_col: str = 'close', steps: int = 1) -> np.ndarray:
        """Make predictions using LSTM model"""
        if self.model is None:
            raise ValueError("Model not trained yet")
            
        # Scale data
        scaled_data = self.scaler.transform(data[[target_col]])
        
        # Get last sequence
        last_sequence = scaled_data[-self.sequence_length:].reshape(1, self.sequence_length, 1)
        
        predictions = []
        current_sequence = last_sequence.copy()
        
        for _ in range(steps):
            pred = self.model.predict(current_sequence, verbose=0)
            predictions.append(pred[0, 0])
            
            # Update sequence for next prediction
            current_sequence = np.roll(current_sequence, -1, axis=1)
            current_sequence[0, -1, 0] = pred[0, 0]
        
        # Inverse transform predictions
        predictions = np.array(predictions).reshape(-1, 1)
        return self.scaler.inverse_transform(predictions).flatten()

class EnsemblePredictor:
    """Ensemble model combining multiple ML algorithms"""
    
    def __init__(self):
        self.models = {}
        self.feature_engineer = FeatureEngineer()
        self.weights = {}
        self.performance_metrics = {}
        
    def initialize_models(self):
        """Initialize all available models"""
        self.models = {
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
        }
        
        if HAS_BOOSTING:
            self.models['xgboost'] = xgb.XGBRegressor(n_estimators=100, random_state=42)
            self.models['lightgbm'] = lgb.LGBMRegressor(n_estimators=100, random_state=42)
        
        if HAS_TENSORFLOW:
            self.models['lstm'] = LSTMPredictor()
    
    def train_models(self, data: pd.DataFrame, target_col: str = 'close') -> Dict[str, ModelPerformance]:
        """Train all models and evaluate performance"""
        # Prepare features
        featured_data = self.feature_engineer.prepare_features(data)
        
        # Select feature columns (exclude target and non-numeric columns)
        feature_cols = [col for col in featured_data.columns 
                       if col not in [target_col, 'timestamp'] and 
                       featured_data[col].dtype in ['int64', 'float64']]
        
        X = featured_data[feature_cols]
        y = featured_data[target_col]
        
        # Split data for time series
        split_idx = int(0.8 * len(X))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        performance_results = {}
        
        for name, model in self.models.items():
            if name == 'lstm':
                # Special handling for LSTM
                lstm_metrics = model.train(featured_data[[target_col]])
                performance_results[name] = ModelPerformance(
                    model_name=name,
                    mse=lstm_metrics['test_loss'],
                    mae=lstm_metrics['test_mae'],
                    r2_score=0.0,  # Will calculate separately
                    accuracy_direction=0.0,  # Will calculate separately
                    sharpe_ratio=0.0  # Will calculate separately
                )
            else:
                # Train traditional ML models
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                
                # Calculate metrics
                mse = mean_squared_error(y_test, y_pred)
                mae = mean_absolute_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)
                
                # Direction accuracy
                actual_direction = (y_test.diff() > 0).astype(int)
                pred_direction = (pd.Series(y_pred).diff() > 0).astype(int)
                direction_accuracy = (actual_direction == pred_direction).mean()
                
                performance_results[name] = ModelPerformance(
                    model_name=name,
                    mse=mse,
                    mae=mae,
                    r2_score=r2,
                    accuracy_direction=direction_accuracy,
                    sharpe_ratio=0.0  # Will calculate based on returns
                )
        
        self.performance_metrics = performance_results
        self._calculate_ensemble_weights()
        
        return performance_results
    
    def _calculate_ensemble_weights(self):
        """Calculate weights for ensemble based on model performance"""
        if not self.performance_metrics:
            return
        
        # Weight based on inverse of MAE (lower MAE = higher weight)
        total_inverse_mae = sum(1 / (metrics.mae + 1e-8) for metrics in self.performance_metrics.values())
        
        self.weights = {
            name: (1 / (metrics.mae + 1e-8)) / total_inverse_mae
            for name, metrics in self.performance_metrics.items()
        }
    
    def predict(self, data: pd.DataFrame, target_col: str = 'close', steps: int = 1) -> PredictionResult:
        """Make ensemble prediction"""
        # Prepare features
        featured_data = self.feature_engineer.prepare_features(data)
        
        # Get feature columns
        feature_cols = [col for col in featured_data.columns 
                       if col not in [target_col, 'timestamp'] and 
                       featured_data[col].dtype in ['int64', 'float64']]
        
        X = featured_data[feature_cols].iloc[-1:] if len(featured_data) > 0 else None
        
        predictions = {}
        
        for name, model in self.models.items():
            if name == 'lstm':
                if len(featured_data) >= 60:  # LSTM needs sequence
                    pred = model.predict(featured_data[[target_col]], steps=steps)
                    predictions[name] = pred[0] if len(pred) > 0 else featured_data[target_col].iloc[-1]
            else:
                if X is not None and len(X) > 0:
                    pred = model.predict(X)
                    predictions[name] = pred[0]
        
        # Ensemble prediction
        if predictions and self.weights:
            ensemble_pred = sum(pred * self.weights.get(name, 0) for name, pred in predictions.items())
        else:
            ensemble_pred = featured_data[target_col].iloc[-1] if len(featured_data) > 0 else 0
        
        # Calculate confidence interval (simplified)
        std_dev = np.std(list(predictions.values())) if predictions else 0
        confidence_interval = (ensemble_pred - 2*std_dev, ensemble_pred + 2*std_dev)
        
        # Calculate confidence score
        confidence_score = max(0, min(1, 1 - (std_dev / ensemble_pred) if ensemble_pred != 0 else 0))
        
        # Feature importance (from best performing model)
        best_model_name = min(self.performance_metrics.keys(), 
                             key=lambda x: self.performance_metrics[x].mae) if self.performance_metrics else None
        
        feature_importance = {}
        if best_model_name and hasattr(self.models[best_model_name], 'feature_importances_'):
            importance_values = self.models[best_model_name].feature_importances_
            feature_importance = dict(zip(feature_cols, importance_values))
        
        return PredictionResult(
            symbol=data.get('symbol', 'UNKNOWN'),
            prediction_date=datetime.now() + timedelta(days=1),
            predicted_price=ensemble_pred,
            confidence_interval=confidence_interval,
            confidence_score=confidence_score,
            model_used='ensemble',
            features_importance=feature_importance,
            technical_signals={},  # Will be filled by technical analysis
            risk_assessment=self._assess_risk(confidence_score, std_dev)
        )
    
    def _assess_risk(self, confidence_score: float, volatility: float) -> str:
        """Assess prediction risk level"""
        if confidence_score > 0.8 and volatility < 0.02:
            return "Low Risk"
        elif confidence_score > 0.6 and volatility < 0.05:
            return "Moderate Risk"
        elif confidence_score > 0.4:
            return "High Risk"
        else:
            return "Very High Risk"
    
    def save_models(self, filepath: str):
        """Save trained models"""
        model_data = {
            'models': {name: model for name, model in self.models.items() if name != 'lstm'},
            'weights': self.weights,
            'performance_metrics': self.performance_metrics,
            'feature_engineer': self.feature_engineer
        }
        joblib.dump(model_data, filepath)
        logger.info(f"Models saved to {filepath}")
    
    def load_models(self, filepath: str):
        """Load trained models"""
        model_data = joblib.load(filepath)
        self.models.update(model_data['models'])
        self.weights = model_data['weights']
        self.performance_metrics = model_data['performance_metrics']
        self.feature_engineer = model_data['feature_engineer']
        logger.info(f"Models loaded from {filepath}")
