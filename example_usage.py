"""
Example usage script for the Indian Stock Trading & Mutual Fund Risk Analysis System
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data.market_data import market_data_provider, web_scraper
from src.features.technical_indicators import TechnicalIndicators
from src.features.fundamental_analysis import buffett_analyzer
from src.features.mutual_fund_analyzer import mf_risk_analyzer
from src.compliance.india_rules import compliance_engine

def main():
    """Demonstrate the system capabilities"""
    
    print("🇮🇳 Indian Stock Trading & Mutual Fund Risk Analysis System")
    print("=" * 70)
    
    # Example 1: Market Data
    print("\n📊 Example 1: Market Data Analysis")
    print("-" * 40)
    
    try:
        # Get market overview
        market_data = market_data_provider.get_market_data()
        print(f"Nifty 50: ₹{market_data.nifty_50:,.0f}")
        print(f"Sensex: ₹{market_data.sensex:,.0f}")
        print(f"Market Breadth: {market_data.market_breadth['advances']} advances, {market_data.market_breadth['declines']} declines")
        
        # Get sector performance
        if market_data.sector_performance:
            print("\nSector Performance:")
            for sector, perf in market_data.sector_performance.items():
                print(f"  {sector}: {perf:.2f}%")
                
    except Exception as e:
        print(f"Market data error: {e}")
    
    # Example 2: Technical Analysis
    print("\n📈 Example 2: Technical Analysis")
    print("-" * 40)
    
    try:
        # Get stock data for TCS
        stock_data = market_data_provider.get_stock_data('TCS', period="5d", interval="1d")
        
        if not stock_data.empty:
            # Calculate technical indicators
            tech_indicators = TechnicalIndicators()
            data_with_indicators = tech_indicators.calculate_all_indicators(stock_data)
            
            # Generate trading signals
            signals = tech_indicators.generate_signals(data_with_indicators)
            
            print(f"TCS Technical Analysis:")
            print(f"  Overall Signal: {signals['overall_signal'].upper()}")
            print(f"  Confidence: {signals['confidence']:.1%}")
            print(f"  RSI Signals: {', '.join(signals['rsi_signals'])}")
            print(f"  MACD Signals: {', '.join(signals['macd_signals'])}")
            
            # Show latest indicators
            latest = data_with_indicators.iloc[-1]
            print(f"  Latest RSI: {latest.get('rsi', 'N/A'):.2f}")
            print(f"  Latest MACD: {latest.get('macd', 'N/A'):.4f}")
            
    except Exception as e:
        print(f"Technical analysis error: {e}")
    
    # Example 3: Buffett-Style Analysis
    print("\n💰 Example 3: Buffett-Style Fundamental Analysis")
    print("-" * 40)
    
    try:
        # Get fundamental data for TCS
        fundamental_data = market_data_provider.get_fundamental_data('TCS')
        
        if fundamental_data:
            # Generate Buffett analysis
            buffett_analysis = buffett_analyzer.generate_buffett_analysis(fundamental_data)
            
            if 'error' not in buffett_analysis:
                print(f"TCS Buffett Analysis:")
                print(f"  Overall Grade: {buffett_analysis['overall_grade']}")
                print(f"  Composite Score: {buffett_analysis['composite_score']:.1f}/100")
                print(f"  Recommendation: {buffett_analysis['recommendation']}")
                print(f"  Confidence: {buffett_analysis['confidence']}")
                
                # Show key insights
                print(f"  Key Insights:")
                for reason in buffett_analysis['reasoning'][:3]:  # Show first 3
                    print(f"    • {reason}")
                    
    except Exception as e:
        print(f"Buffett analysis error: {e}")
    
    # Example 4: Compliance Check
    print("\n🔒 Example 4: India-Specific Compliance Check")
    print("-" * 40)
    
    try:
        # Check compliance for TCS
        compliance_rules = compliance_engine.get_compliance_rules('TCS.NS')
        
        print(f"TCS Compliance Rules:")
        print(f"  Short Selling Allowed: {'✅ Yes' if compliance_rules.short_selling_allowed else '❌ No'}")
        print(f"  SLB Eligible: {'✅ Yes' if compliance_rules.slb_eligible else '❌ No'}")
        print(f"  Margin Requirement: {compliance_rules.margin_requirement:.1%}")
        print(f"  Auto Square-off Time: {compliance_rules.auto_square_off_time}")
        print(f"  Circuit Limit: {compliance_rules.circuit_limit:.1%}")
        print(f"  Overnight Short Allowed: {'❌ No' if not compliance_rules.overnight_short_allowed else '⚠️ Yes'}")
        
    except Exception as e:
        print(f"Compliance check error: {e}")
    
    # Example 5: Mutual Fund Risk Analysis
    print("\n🏦 Example 5: Mutual Fund Risk Analysis")
    print("-" * 40)
    
    try:
        # Sample fund data
        sample_fund = {
            'fund_name': 'HDFC Mid-Cap Opportunities',
            'isin': 'HDFM0000001',
            'category': 'Equity - Mid Cap',
            'asset_allocation': {
                'equity': 0.90,
                'debt': 0.05,
                'others': 0.05
            },
            'market_cap_exposure': {
                'Large Cap': 0.20,
                'Mid Cap': 0.60,
                'Small Cap': 0.20
            },
            'sector_exposure': {
                'Banking': 0.25,
                'IT': 0.20,
                'FMCG': 0.15,
                'Others': 0.40
            },
            'sector_concentration': 0.25
        }
        
        # Generate risk analysis
        risk_analysis = mf_risk_analyzer.generate_risk_analysis(sample_fund)
        
        if 'error' not in risk_analysis:
            print(f"Fund Risk Analysis:")
            print(f"  Fund: {risk_analysis['fund_name']}")
            print(f"  Riskometer Level: {risk_analysis['riskometer_level']}")
            print(f"  Risk Score: {risk_analysis['composite_risk_score']:.1f}/100")
            
            # Show risk breakdown
            risk_breakdown = risk_analysis['risk_breakdown']
            print(f"  Risk Breakdown:")
            print(f"    • Equity Risk: {risk_breakdown['equity_risk']:.1f}")
            print(f"    • Debt Risk: {risk_breakdown['debt_risk']:.1f}")
            print(f"    • Other Risk: {risk_breakdown['other_risk']:.1f}")
            
            # Show risk factors
            print(f"  Key Risk Factors:")
            for factor in risk_analysis['risk_factors'][:3]:  # Show first 3
                print(f"    • {factor}")
                
    except Exception as e:
        print(f"Mutual fund analysis error: {e}")
    
    # Example 6: Margin and Leverage Calculation
    print("\n💳 Example 6: Margin and Leverage Calculation")
    print("-" * 40)
    
    try:
        # Example position
        position_value = 100000  # 1 lakh
        available_margin = 25000  # 25k
        
        # Check margin requirements
        margin_sufficient, required_margin, margin_msg = compliance_engine.check_margin_requirements(
            position_value, available_margin
        )
        
        print(f"Position Analysis:")
        print(f"  Position Value: ₹{position_value:,.0f}")
        print(f"  Available Margin: ₹{available_margin:,.0f}")
        print(f"  Required Margin: ₹{required_margin:,.0f}")
        print(f"  Margin Status: {'✅ Sufficient' if margin_sufficient else '❌ Insufficient'}")
        print(f"  Message: {margin_msg}")
        
        # Calculate leverage
        leverage = compliance_engine.calculate_leverage(position_value, available_margin)
        print(f"  Effective Leverage: {leverage:.2f}x")
        
        # Check auto square-off timing
        from datetime import time
        current_time = time(14, 30)  # 2:30 PM
        should_warn, timing_msg = compliance_engine.check_auto_square_off_timing(current_time)
        print(f"  Auto Square-off: {timing_msg}")
        
    except Exception as e:
        print(f"Margin calculation error: {e}")
    
    print("\n" + "=" * 70)
    print("🎉 System demonstration completed!")
    print("\nTo run the full dashboard:")
    print("  streamlit run src/ui/main.py")
    print("\nTo install dependencies:")
    print("  pip install -r requirements.txt")

if __name__ == "__main__":
    main() 