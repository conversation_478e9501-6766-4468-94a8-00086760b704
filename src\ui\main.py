"""
Main Streamlit Dashboard for Indian Stock Trading & Mutual Fund Risk Analysis
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import numpy as np
from datetime import datetime, timedelta
import time

# Import our modules
from ..data.market_data import market_data_provider, web_scraper
from ..features.technical_indicators import TechnicalIndicators
from ..features.fundamental_analysis import buffett_analyzer
from ..features.mutual_fund_analyzer import mf_risk_analyzer
from ..compliance.india_rules import compliance_engine

# Page configuration
st.set_page_config(
    page_title="Indian Stock Trading & MF Risk Analysis",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .risk-high { color: #ff4500; }
    .risk-moderate { color: #ffa500; }
    .risk-low { color: #90ee90; }
    .risk-very-low { color: #00ff00; }
</style>
""", unsafe_allow_html=True)

def main():
    """Main dashboard function"""
    
    # Header
    st.markdown('<h1 class="main-header">🇮🇳 Indian Stock Trading & Mutual Fund Risk Analysis</h1>', 
                unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.header("📊 Dashboard Navigation")
        page = st.selectbox(
            "Select Module",
            ["🏠 Dashboard Overview", "📈 Intraday Trading", "💰 Buffett Analysis", "🏦 Mutual Fund Risk", "⚙️ Settings"]
        )
        
        st.header("🔧 Quick Actions")
        if st.button("🔄 Refresh Market Data"):
            st.rerun()
        
        st.header("ℹ️ About")
        st.info("""
        This system provides comprehensive analysis for Indian markets:
        - **Intraday Trading**: Technical signals & compliance
        - **Buffett Analysis**: Fundamental stock screening
        - **MF Risk**: SEBI/AMFI Riskometer compliance
        """)
    
    # Main content based on selected page
    if page == "🏠 Dashboard Overview":
        show_dashboard_overview()
    elif page == "📈 Intraday Trading":
        show_intraday_trading()
    elif page == "💰 Buffett Analysis":
        show_buffett_analysis()
    elif page == "🏦 Mutual Fund Risk":
        show_mutual_fund_risk()
    elif page == "⚙️ Settings":
        show_settings()

def show_dashboard_overview():
    """Show dashboard overview with market summary"""
    
    st.header("📊 Market Overview")
    
    # Market data
    try:
        market_data = market_data_provider.get_market_data()
        
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Nifty 50",
                f"₹{market_data.nifty_50:,.0f}",
                delta=f"{market_data.nifty_50 * 0.01:,.0f}"  # Placeholder
            )
        
        with col2:
            st.metric(
                "Sensex",
                f"₹{market_data.sensex:,.0f}",
                delta=f"{market_data.sensex * 0.01:,.0f}"  # Placeholder
            )
        
        with col3:
            st.metric(
                "Advance/Decline",
                f"{market_data.market_breadth['advances']}/{market_data.market_breadth['declines']}",
                delta=f"{market_data.advance_decline_ratio:.2f}"
            )
        
        with col4:
            st.metric(
                "Market Breadth",
                f"{market_data.market_breadth['total']} stocks",
                delta="Neutral"
            )
        
        # Sector performance
        st.subheader("📈 Sector Performance")
        if market_data.sector_performance:
            sector_df = pd.DataFrame([
                {"Sector": sector, "Performance": perf}
                for sector, perf in market_data.sector_performance.items()
            ])
            
            fig = px.bar(
                sector_df, 
                x="Sector", 
                y="Performance",
                title="Sector-wise Performance (%)",
                color="Performance",
                color_continuous_scale="RdYlGn"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Market news
        st.subheader("📰 Latest Market News")
        try:
            news = web_scraper.get_market_news()
            if news:
                for i, item in enumerate(news[:5]):
                    with st.expander(f"{item['title'][:50]}..."):
                        st.write(f"**Source:** {item['source']}")
                        st.write(f"**Time:** {item['timestamp']}")
                        if st.button(f"Read More {i}"):
                            st.write(item['title'])
            else:
                st.info("News data temporarily unavailable")
        except Exception as e:
            st.warning(f"News data error: {str(e)}")
            
    except Exception as e:
        st.error(f"Error loading market data: {str(e)}")

def show_intraday_trading():
    """Show intraday trading module"""
    
    st.header("📈 Intraday Trading Assistant")
    
    # Stock input
    col1, col2 = st.columns([2, 1])
    
    with col1:
        stock_symbol = st.text_input(
            "Enter Stock Symbol",
            placeholder="e.g., TCS, RELIANCE, HDFCBANK",
            help="Enter stock symbol without .NS suffix"
        )
    
    with col2:
        timeframe = st.selectbox(
            "Timeframe",
            ["1m", "5m", "15m", "30m", "1h"],
            help="Select intraday timeframe"
        )
    
    if stock_symbol and st.button("🔍 Analyze Stock"):
        try:
            # Get stock data
            data = market_data_provider.get_stock_data(
                stock_symbol, 
                period="1d", 
                interval=timeframe
            )
            
            if data.empty:
                st.error(f"No data available for {stock_symbol}")
                return
            
            # Calculate technical indicators
            tech_indicators = TechnicalIndicators()
            data_with_indicators = tech_indicators.calculate_all_indicators(data)
            
            # Generate signals
            signals = tech_indicators.generate_signals(data_with_indicators)
            
            # Display results
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.subheader("📊 Technical Analysis")
                
                # Price chart
                fig = make_subplots(
                    rows=3, cols=1,
                    shared_xaxes=True,
                    vertical_spacing=0.05,
                    subplot_titles=('Price & VWAP', 'RSI', 'MACD'),
                    row_heights=[0.6, 0.2, 0.2]
                )
                
                # Candlestick chart
                fig.add_trace(
                    go.Candlestick(
                        x=data_with_indicators['timestamp'],
                        open=data_with_indicators['open'],
                        high=data_with_indicators['high'],
                        low=data_with_indicators['low'],
                        close=data_with_indicators['close'],
                        name="OHLC"
                    ),
                    row=1, col=1
                )
                
                # VWAP
                if 'vwap' in data_with_indicators.columns:
                    fig.add_trace(
                        go.Scatter(
                            x=data_with_indicators['timestamp'],
                            y=data_with_indicators['vwap'],
                            name="VWAP",
                            line=dict(color='purple')
                        ),
                        row=1, col=1
                    )
                
                # RSI
                if 'rsi' in data_with_indicators.columns:
                    fig.add_trace(
                        go.Scatter(
                            x=data_with_indicators['timestamp'],
                            y=data_with_indicators['rsi'],
                            name="RSI",
                            line=dict(color='blue')
                        ),
                        row=2, col=1
                    )
                    
                    # RSI overbought/oversold lines
                    fig.add_hline(y=70, line_dash="dash", line_color="red", row=2, col=1)
                    fig.add_hline(y=30, line_dash="dash", line_color="green", row=2, col=1)
                
                # MACD
                if 'macd' in data_with_indicators.columns and 'macd_signal' in data_with_indicators.columns:
                    fig.add_trace(
                        go.Scatter(
                            x=data_with_indicators['timestamp'],
                            y=data_with_indicators['macd'],
                            name="MACD",
                            line=dict(color='blue')
                        ),
                        row=3, col=1
                    )
                    
                    fig.add_trace(
                        go.Scatter(
                            x=data_with_indicators['timestamp'],
                            y=data_with_indicators['macd_signal'],
                            name="Signal",
                            line=dict(color='red')
                        ),
                        row=3, col=1
                    )
                
                fig.update_layout(height=600, showlegend=True)
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                st.subheader("🎯 Trading Signals")
                
                # Overall signal
                signal_color = {
                    'buy': 'green',
                    'sell': 'red',
                    'hold': 'orange'
                }
                
                st.markdown(f"""
                <div class="metric-card">
                    <h3>Signal: <span style="color: {signal_color.get(signals['overall_signal'], 'black')}">
                    {signals['overall_signal'].upper()}</span></h3>
                    <p>Confidence: {signals['confidence']:.1%}</p>
                </div>
                """, unsafe_allow_html=True)
                
                # Signal breakdown
                st.write("**RSI Signals:**")
                for signal in signals['rsi_signals']:
                    st.write(f"• {signal}")
                
                st.write("**MACD Signals:**")
                for signal in signals['macd_signals']:
                    st.write(f"• {signal}")
                
                st.write("**Moving Average Signals:**")
                for signal in signals['ma_signals']:
                    st.write(f"• {signal}")
                
                # Compliance check
                st.subheader("🔒 Compliance Check")
                compliance_rules = compliance_engine.get_compliance_rules(f"{stock_symbol}.NS")
                
                st.write(f"**Short Selling:** {'✅ Allowed' if compliance_rules.short_selling_allowed else '❌ Not Allowed'}")
                st.write(f"**Margin Required:** {compliance_rules.margin_requirement:.1%}")
                st.write(f"**Auto Square-off:** {compliance_rules.auto_square_off_time}")
                
        except Exception as e:
            st.error(f"Error analyzing stock: {str(e)}")

def show_buffett_analysis():
    """Show Buffett-style fundamental analysis"""
    
    st.header("💰 Buffett-Style Fundamental Analysis")
    
    # Stock input
    col1, col2 = st.columns([2, 1])
    
    with col1:
        stock_symbol = st.text_input(
            "Enter Stock Symbol for Analysis",
            placeholder="e.g., TCS, RELIANCE, HDFCBANK",
            help="Enter stock symbol for fundamental analysis"
        )
    
    with col2:
        analysis_type = st.selectbox(
            "Analysis Type",
            ["Single Stock", "Sector Comparison", "Market Scanner"]
        )
    
    if stock_symbol and st.button("🔍 Analyze Fundamentals"):
        try:
            # Get fundamental data
            fundamental_data = market_data_provider.get_fundamental_data(stock_symbol)
            
            if not fundamental_data:
                st.error(f"No fundamental data available for {stock_symbol}")
                return
            
            # Generate Buffett analysis
            buffett_analysis = buffett_analyzer.generate_buffett_analysis(fundamental_data)
            
            if 'error' in buffett_analysis:
                st.error(f"Analysis error: {buffett_analysis['error']}")
                return
            
            # Display results
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.subheader("📊 Analysis Results")
                
                # Overall grade
                grade_color = {
                    'A+': '#00ff00',
                    'A': '#90ee90',
                    'B+': '#ffff00',
                    'B': '#ffa500',
                    'C+': '#ff4500',
                    'C': '#ff0000',
                    'D/F': '#8b0000'
                }
                
                st.markdown(f"""
                <div class="metric-card">
                    <h2>Overall Grade: <span style="color: {grade_color.get(buffett_analysis['overall_grade'], 'black')}">
                    {buffett_analysis['overall_grade']}</span></h2>
                    <h3>Composite Score: {buffett_analysis['composite_score']:.1f}/100</h3>
                    <p><strong>Recommendation:</strong> {buffett_analysis['recommendation']}</p>
                    <p><strong>Confidence:</strong> {buffett_analysis['confidence']}</p>
                </div>
                """, unsafe_allow_html=True)
                
                # Score breakdown
                st.subheader("📈 Score Breakdown")
                
                # Quality score
                quality_score = buffett_analysis['quality_score']
                st.write("**Quality Score:**")
                col_q1, col_q2 = st.columns(2)
                with col_q1:
                    st.metric("ROE Score", f"{quality_score['roe_score']:.1f}/25")
                    st.metric("Debt Score", f"{quality_score['debt_score']:.1f}/20")
                with col_q2:
                    st.metric("Profitability", f"{quality_score['profitability_score']:.1f}/20")
                    st.metric("Growth Score", f"{quality_score['growth_score']:.1f}/20")
                
                # Valuation score
                valuation_score = buffett_analysis['valuation_score']
                st.write("**Valuation Score:**")
                col_v1, col_v2 = st.columns(2)
                with col_v1:
                    st.metric("PE Score", f"{valuation_score['pe_score']:.1f}/30")
                    st.metric("PB Score", f"{valuation_score['pb_score']:.1f}/25")
                with col_v2:
                    st.metric("Dividend Score", f"{valuation_score['dividend_score']:.1f}/20")
                    st.metric("Market Cap Score", f"{valuation_score['market_cap_score']:.1f}/25")
                
                # Moat score
                moat_score = buffett_analysis['moat_score']
                st.write("**Moat Score:**")
                col_m1, col_m2 = st.columns(2)
                with col_m1:
                    st.metric("ROE Consistency", f"{moat_score['roe_consistency_score']:.1f}/30")
                    st.metric("Margin Stability", f"{moat_score['profit_margin_stability_score']:.1f}/25")
                with col_m2:
                    st.metric("Market Share", f"{moat_score['market_share_score']:.1f}/25")
                    st.metric("Brand Value", f"{moat_score['brand_value_score']:.1f}/20")
            
            with col2:
                st.subheader("💡 Key Insights")
                
                # Reasoning
                for reason in buffett_analysis['reasoning']:
                    st.info(reason)
                
                # Fundamental metrics
                st.subheader("📋 Fundamental Metrics")
                st.write(f"**PE Ratio:** {fundamental_data.get('pe_ratio', 'N/A')}")
                st.write(f"**PB Ratio:** {fundamental_data.get('pb_ratio', 'N/A')}")
                st.write(f"**ROE:** {fundamental_data.get('roe', 'N/A')}%")
                st.write(f"**Debt/Equity:** {fundamental_data.get('debt_to_equity', 'N/A')}")
                st.write(f"**Market Cap:** ₹{fundamental_data.get('market_cap', 0)/10000000:,.0f} Cr")
                st.write(f"**Sector:** {fundamental_data.get('sector', 'N/A')}")
                
        except Exception as e:
            st.error(f"Error in fundamental analysis: {str(e)}")

def show_mutual_fund_risk():
    """Show mutual fund risk analysis"""
    
    st.header("🏦 Mutual Fund Risk Calculator")
    
    # Fund input
    col1, col2 = st.columns([2, 1])
    
    with col1:
        fund_name = st.text_input(
            "Enter Fund Name or ISIN",
            placeholder="e.g., HDFC Mid-Cap Opportunities, Axis Bluechip",
            help="Enter mutual fund name or ISIN for risk analysis"
        )
    
    with col2:
        analysis_mode = st.selectbox(
            "Analysis Mode",
            ["Single Fund", "Fund Comparison", "Portfolio Risk"]
        )
    
    if fund_name and st.button("🔍 Analyze Fund Risk"):
        try:
            # Sample fund data (in real implementation, this would come from API)
            sample_fund_data = {
                'fund_name': fund_name,
                'isin': 'SAMPLE123',
                'category': 'Equity - Large Cap',
                'asset_allocation': {
                    'equity': 0.85,
                    'debt': 0.10,
                    'others': 0.05
                },
                'market_cap_exposure': {
                    'Large Cap': 0.70,
                    'Mid Cap': 0.20,
                    'Small Cap': 0.10
                },
                'sector_exposure': {
                    'Banking': 0.25,
                    'IT': 0.20,
                    'FMCG': 0.15,
                    'Others': 0.40
                },
                'sector_concentration': 0.25
            }
            
            # Generate risk analysis
            risk_analysis = mf_risk_analyzer.generate_risk_analysis(sample_fund_data)
            
            if 'error' in risk_analysis:
                st.error(f"Risk analysis error: {risk_analysis['error']}")
                return
            
            # Display results
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.subheader("📊 Risk Analysis Results")
                
                # Riskometer display
                risk_level = risk_analysis['riskometer_level']
                risk_color = {
                    'Very Low': '#00ff00',
                    'Low': '#90ee90',
                    'Moderate': '#ffff00',
                    'Moderately High': '#ffa500',
                    'High': '#ff4500',
                    'Very High': '#ff0000'
                }
                
                st.markdown(f"""
                <div class="metric-card">
                    <h2>SEBI/AMFI Riskometer: <span style="color: {risk_color.get(risk_level, 'black')}">
                    {risk_level}</span></h2>
                    <h3>Risk Score: {risk_analysis['composite_risk_score']:.1f}/100</h3>
                    <p><strong>Category:</strong> {sample_fund_data['category']}</p>
                </div>
                """, unsafe_allow_html=True)
                
                # Risk breakdown
                st.subheader("🔍 Risk Breakdown")
                
                risk_breakdown = risk_analysis['risk_breakdown']
                col_r1, col_r2, col_r3 = st.columns(3)
                
                with col_r1:
                    st.metric("Equity Risk", f"{risk_breakdown['equity_risk']:.1f}")
                with col_r2:
                    st.metric("Debt Risk", f"{risk_breakdown['debt_risk']:.1f}")
                with col_r3:
                    st.metric("Other Risk", f"{risk_breakdown['other_risk']:.1f}")
                
                # Asset allocation chart
                st.subheader("📈 Asset Allocation")
                asset_allocation = sample_fund_data['asset_allocation']
                
                fig = px.pie(
                    values=list(asset_allocation.values()),
                    names=list(asset_allocation.keys()),
                    title="Fund Asset Allocation"
                )
                st.plotly_chart(fig, use_container_width=True)
                
                # Market cap exposure
                st.subheader("🏢 Market Cap Exposure")
                market_cap_data = sample_fund_data['market_cap_exposure']
                
                fig = px.bar(
                    x=list(market_cap_data.keys()),
                    y=list(market_cap_data.values()),
                    title="Market Cap Exposure (%)",
                    color=list(market_cap_data.values()),
                    color_continuous_scale="RdYlGn"
                )
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                st.subheader("⚠️ Risk Factors")
                
                # Risk factors
                for factor in risk_analysis['risk_factors']:
                    st.warning(factor)
                
                # Fund details
                st.subheader("📋 Fund Details")
                st.write(f"**Fund Name:** {sample_fund_data['fund_name']}")
                st.write(f"**Category:** {sample_fund_data['category']}")
                st.write(f"**Equity Exposure:** {sample_fund_data['asset_allocation']['equity']:.1%}")
                st.write(f"**Debt Exposure:** {sample_fund_data['asset_allocation']['debt']:.1%}")
                st.write(f"**Small Cap Exposure:** {sample_fund_data['market_cap_exposure']['Small Cap']:.1%}")
                
                # Compliance info
                st.subheader("🔒 SEBI Compliance")
                st.success("✅ Riskometer compliant")
                st.info("📅 Monthly evaluation required")
                st.info("📊 Quantitative methodology")
                
        except Exception as e:
            st.error(f"Error in mutual fund risk analysis: {str(e)}")

def show_settings():
    """Show settings and configuration"""
    
    st.header("⚙️ Settings & Configuration")
    
    st.subheader("🔧 System Configuration")
    
    # Broker settings
    st.write("**Broker Configuration:**")
    broker_name = st.text_input("Broker Name", value="Default")
    auto_square_off = st.time_input("Auto Square-off Time", value=datetime.strptime("15:15", "%H:%M").time())
    margin_requirement = st.slider("Margin Requirement (%)", 20, 50, 20)
    
    # Analysis settings
    st.write("**Analysis Parameters:**")
    rsi_period = st.slider("RSI Period", 10, 20, 14)
    macd_fast = st.slider("MACD Fast Period", 8, 16, 12)
    macd_slow = st.slider("MACD Slow Period", 20, 30, 26)
    
    # Risk tolerance
    st.write("**Risk Tolerance:**")
    risk_tolerance = st.selectbox(
        "Risk Profile",
        ["Conservative", "Moderate", "Aggressive"],
        help="Select your risk tolerance level"
    )
    
    if st.button("💾 Save Settings"):
        st.success("Settings saved successfully!")
        st.info("Note: Some settings require system restart to take effect.")

if __name__ == "__main__":
    main() 