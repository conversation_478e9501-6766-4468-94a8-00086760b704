"""
AI-Powered Intraday Trading Assistant for Indian Markets
Implements SEBI-compliant intraday trading with AI-driven signals and risk management
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta, time
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# Technical Analysis
try:
    import talib
    HAS_TALIB = True
except ImportError:
    HAS_TALIB = False

# ML Libraries
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import joblib

from loguru import logger

class SignalType(Enum):
    """Trading signal types"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    EXIT = "EXIT"

class RiskLevel(Enum):
    """Risk assessment levels"""
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"
    VERY_HIGH = "Very High"

@dataclass
class TradingSignal:
    """Comprehensive trading signal"""
    symbol: str
    signal_type: SignalType
    entry_price: float
    target_price: float
    stop_loss: float
    quantity: int
    confidence: float
    risk_level: RiskLevel
    reasoning: List[str]
    timestamp: datetime
    expiry_time: datetime
    margin_required: float
    leverage: float
    
@dataclass
class MarketConditions:
    """Current market conditions"""
    market_trend: str
    volatility: float
    volume_surge: bool
    news_sentiment: float
    sector_performance: Dict[str, float]
    fii_activity: str
    vix_level: float

@dataclass
class ComplianceCheck:
    """SEBI compliance verification"""
    is_eligible_for_shorting: bool
    margin_requirement: float
    auto_square_off_time: time
    circuit_limit_status: str
    delivery_obligation: bool
    slb_availability: bool

class IndianMarketCompliance:
    """SEBI and NSE compliance engine"""
    
    def __init__(self):
        self.trading_hours = {
            'market_open': time(9, 15),
            'market_close': time(15, 30),
            'pre_market_open': time(9, 0),
            'pre_market_close': time(9, 15),
            'auto_square_off': time(15, 20)  # 10 minutes before market close
        }
        
        # F&O eligible stocks (simplified list)
        self.fo_eligible_stocks = {
            'TCS.NS', 'RELIANCE.NS', 'HDFCBANK.NS', 'INFY.NS', 'ICICIBANK.NS',
            'WIPRO.NS', 'LT.NS', 'SBIN.NS', 'ITC.NS', 'HINDUNILVR.NS',
            'BAJFINANCE.NS', 'KOTAKBANK.NS', 'ASIANPAINT.NS', 'MARUTI.NS'
        }
        
        # Circuit limits (simplified)
        self.circuit_limits = {
            'upper_limit': 0.20,  # 20% upper circuit
            'lower_limit': -0.20  # 20% lower circuit
        }
    
    def check_compliance(self, symbol: str, signal_type: SignalType, current_time: datetime) -> ComplianceCheck:
        """Comprehensive compliance check"""
        
        # Check if stock is eligible for shorting
        is_eligible_for_shorting = symbol in self.fo_eligible_stocks
        
        # Calculate margin requirement (simplified)
        if signal_type == SignalType.SELL:  # Short selling
            margin_requirement = 0.20  # 20% margin for short selling
        else:
            margin_requirement = 0.20  # 20% margin for intraday buying
        
        # Auto square-off time
        auto_square_off_time = self.trading_hours['auto_square_off']
        
        # Circuit limit status (mock)
        circuit_limit_status = "Normal"  # Would check real-time circuit limits
        
        # Delivery obligation for short selling
        delivery_obligation = signal_type == SignalType.SELL
        
        # SLB (Securities Lending and Borrowing) availability
        slb_availability = symbol in self.fo_eligible_stocks
        
        return ComplianceCheck(
            is_eligible_for_shorting=is_eligible_for_shorting,
            margin_requirement=margin_requirement,
            auto_square_off_time=auto_square_off_time,
            circuit_limit_status=circuit_limit_status,
            delivery_obligation=delivery_obligation,
            slb_availability=slb_availability
        )
    
    def is_trading_time(self, current_time: datetime) -> bool:
        """Check if current time is within trading hours"""
        current_time_only = current_time.time()
        return (self.trading_hours['market_open'] <= current_time_only <= self.trading_hours['market_close'])
    
    def time_to_square_off(self, current_time: datetime) -> timedelta:
        """Calculate time remaining for auto square-off"""
        today = current_time.date()
        square_off_datetime = datetime.combine(today, self.trading_hours['auto_square_off'])
        
        if current_time < square_off_datetime:
            return square_off_datetime - current_time
        else:
            return timedelta(0)

class TechnicalAnalysisAI:
    """AI-enhanced technical analysis"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.signal_classifier = None
        self._initialize_ml_model()
    
    def _initialize_ml_model(self):
        """Initialize ML model for signal classification"""
        # This would be trained on historical data
        self.signal_classifier = RandomForestClassifier(
            n_estimators=100,
            random_state=42,
            class_weight='balanced'
        )
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate comprehensive technical indicators"""
        df = data.copy()
        
        # Price-based indicators
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Moving averages
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        
        # VWAP (Volume Weighted Average Price)
        df['vwap'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3).cumsum() / df['volume'].cumsum()
        
        # Volatility
        df['atr'] = self._calculate_atr(df)
        df['volatility'] = df['returns'].rolling(20).std()
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        if HAS_TALIB:
            # RSI
            df['rsi'] = talib.RSI(df['close'].values, timeperiod=14)
            
            # MACD
            df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'].values)
            
            # Bollinger Bands
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'].values)
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Stochastic
            df['stoch_k'], df['stoch_d'] = talib.STOCH(df['high'].values, df['low'].values, df['close'].values)
            
            # Williams %R
            df['williams_r'] = talib.WILLR(df['high'].values, df['low'].values, df['close'].values)
            
            # ADX (Average Directional Index)
            df['adx'] = talib.ADX(df['high'].values, df['low'].values, df['close'].values)
            
        else:
            # Fallback calculations without TA-Lib
            df['rsi'] = self._calculate_rsi(df['close'])
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # Support and Resistance
        df['support'] = df['low'].rolling(20).min()
        df['resistance'] = df['high'].rolling(20).max()
        
        # Price position relative to support/resistance
        df['support_distance'] = (df['close'] - df['support']) / df['close']
        df['resistance_distance'] = (df['resistance'] - df['close']) / df['close']
        
        return df
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI without TA-Lib"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def generate_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Generate AI-enhanced trading signals"""
        if len(data) < 50:  # Need sufficient data
            return {'signal': SignalType.HOLD, 'confidence': 0.0, 'reasoning': ['Insufficient data']}
        
        latest = data.iloc[-1]
        prev = data.iloc[-2]
        
        signals = []
        reasoning = []
        confidence_factors = []
        
        # RSI signals
        if 'rsi' in data.columns:
            rsi = latest['rsi']
            if rsi < 30:
                signals.append('BUY')
                reasoning.append(f"RSI oversold at {rsi:.1f}")
                confidence_factors.append(0.8)
            elif rsi > 70:
                signals.append('SELL')
                reasoning.append(f"RSI overbought at {rsi:.1f}")
                confidence_factors.append(0.8)
        
        # MACD signals
        if 'macd' in data.columns and 'macd_signal' in data.columns:
            macd_cross = (latest['macd'] > latest['macd_signal']) and (prev['macd'] <= prev['macd_signal'])
            macd_cross_down = (latest['macd'] < latest['macd_signal']) and (prev['macd'] >= prev['macd_signal'])
            
            if macd_cross:
                signals.append('BUY')
                reasoning.append("MACD bullish crossover")
                confidence_factors.append(0.7)
            elif macd_cross_down:
                signals.append('SELL')
                reasoning.append("MACD bearish crossover")
                confidence_factors.append(0.7)
        
        # VWAP signals
        if 'vwap' in data.columns:
            if latest['close'] > latest['vwap'] and prev['close'] <= prev['vwap']:
                signals.append('BUY')
                reasoning.append("Price crossed above VWAP")
                confidence_factors.append(0.6)
            elif latest['close'] < latest['vwap'] and prev['close'] >= prev['vwap']:
                signals.append('SELL')
                reasoning.append("Price crossed below VWAP")
                confidence_factors.append(0.6)
        
        # Volume confirmation
        if 'volume_ratio' in data.columns:
            if latest['volume_ratio'] > 1.5:
                reasoning.append("High volume confirmation")
                confidence_factors.append(0.3)
        
        # Moving average signals
        if latest['close'] > latest['sma_5'] > latest['sma_10'] > latest['sma_20']:
            signals.append('BUY')
            reasoning.append("Strong uptrend (MA alignment)")
            confidence_factors.append(0.5)
        elif latest['close'] < latest['sma_5'] < latest['sma_10'] < latest['sma_20']:
            signals.append('SELL')
            reasoning.append("Strong downtrend (MA alignment)")
            confidence_factors.append(0.5)
        
        # Determine final signal
        buy_signals = signals.count('BUY')
        sell_signals = signals.count('SELL')
        
        if buy_signals > sell_signals:
            final_signal = SignalType.BUY
        elif sell_signals > buy_signals:
            final_signal = SignalType.SELL
        else:
            final_signal = SignalType.HOLD
        
        # Calculate confidence
        confidence = np.mean(confidence_factors) if confidence_factors else 0.0
        confidence = min(confidence * (abs(buy_signals - sell_signals) + 1) / 2, 1.0)
        
        return {
            'signal': final_signal,
            'confidence': confidence,
            'reasoning': reasoning,
            'technical_score': confidence * 100,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }

class IntradayTradingAI:
    """Main AI-powered intraday trading system"""
    
    def __init__(self):
        self.compliance_engine = IndianMarketCompliance()
        self.technical_analyzer = TechnicalAnalysisAI()
        self.position_sizer = PositionSizer()
        self.risk_manager = RiskManager()
        
    def analyze_stock(self, symbol: str, data: pd.DataFrame, market_conditions: MarketConditions) -> TradingSignal:
        """Comprehensive stock analysis for intraday trading"""
        
        # Technical analysis
        data_with_indicators = self.technical_analyzer.calculate_indicators(data)
        technical_signals = self.technical_analyzer.generate_signals(data_with_indicators)
        
        # Market condition analysis
        market_score = self._analyze_market_conditions(market_conditions)
        
        # Combine signals
        final_signal_type = self._combine_signals(technical_signals, market_score)
        
        # Compliance check
        compliance = self.compliance_engine.check_compliance(
            symbol, final_signal_type, datetime.now()
        )
        
        # Risk assessment
        risk_level = self.risk_manager.assess_risk(
            symbol, data_with_indicators, market_conditions, final_signal_type
        )
        
        # Position sizing
        current_price = data['close'].iloc[-1]
        position_info = self.position_sizer.calculate_position(
            symbol, current_price, final_signal_type, risk_level, compliance.margin_requirement
        )
        
        # Generate trading signal
        trading_signal = self._generate_trading_signal(
            symbol, final_signal_type, current_price, position_info,
            technical_signals, compliance, risk_level, market_conditions
        )
        
        return trading_signal
    
    def _analyze_market_conditions(self, conditions: MarketConditions) -> float:
        """Analyze overall market conditions"""
        score = 0.0
        
        # Market trend
        if conditions.market_trend == 'bullish':
            score += 0.3
        elif conditions.market_trend == 'bearish':
            score -= 0.3
        
        # Volatility
        if 0.01 <= conditions.volatility <= 0.03:  # Optimal volatility range
            score += 0.2
        elif conditions.volatility > 0.05:  # Too volatile
            score -= 0.2
        
        # VIX level
        if conditions.vix_level < 15:  # Low fear
            score += 0.1
        elif conditions.vix_level > 25:  # High fear
            score -= 0.2
        
        # News sentiment
        score += conditions.news_sentiment * 0.2
        
        # Volume surge
        if conditions.volume_surge:
            score += 0.1
        
        # FII activity
        if conditions.fii_activity == 'buying':
            score += 0.1
        elif conditions.fii_activity == 'selling':
            score -= 0.1
        
        return max(-1.0, min(1.0, score))  # Normalize to [-1, 1]
    
    def _combine_signals(self, technical_signals: Dict, market_score: float) -> SignalType:
        """Combine technical and market signals"""
        technical_signal = technical_signals['signal']
        confidence = technical_signals['confidence']
        
        # Adjust confidence based on market conditions
        adjusted_confidence = confidence * (1 + market_score * 0.5)
        
        # Decision logic
        if technical_signal == SignalType.BUY and adjusted_confidence > 0.6 and market_score > -0.3:
            return SignalType.BUY
        elif technical_signal == SignalType.SELL and adjusted_confidence > 0.6 and market_score < 0.3:
            return SignalType.SELL
        else:
            return SignalType.HOLD
    
    def _generate_trading_signal(self, symbol: str, signal_type: SignalType, current_price: float,
                                position_info: Dict, technical_signals: Dict, compliance: ComplianceCheck,
                                risk_level: RiskLevel, market_conditions: MarketConditions) -> TradingSignal:
        """Generate comprehensive trading signal"""
        
        # Calculate target and stop loss
        atr_multiplier = 2.0 if risk_level == RiskLevel.LOW else 1.5
        atr_estimate = current_price * 0.02  # Simplified ATR estimate
        
        if signal_type == SignalType.BUY:
            target_price = current_price * (1 + 0.02)  # 2% target
            stop_loss = current_price * (1 - 0.01)    # 1% stop loss
        elif signal_type == SignalType.SELL:
            target_price = current_price * (1 - 0.02)  # 2% target
            stop_loss = current_price * (1 + 0.01)    # 1% stop loss
        else:
            target_price = current_price
            stop_loss = current_price
        
        # Expiry time (auto square-off)
        expiry_time = datetime.combine(
            datetime.now().date(),
            compliance.auto_square_off_time
        )
        
        # Leverage calculation
        leverage = 1 / compliance.margin_requirement
        
        return TradingSignal(
            symbol=symbol,
            signal_type=signal_type,
            entry_price=current_price,
            target_price=target_price,
            stop_loss=stop_loss,
            quantity=position_info['quantity'],
            confidence=technical_signals['confidence'],
            risk_level=risk_level,
            reasoning=technical_signals['reasoning'],
            timestamp=datetime.now(),
            expiry_time=expiry_time,
            margin_required=position_info['margin_required'],
            leverage=leverage
        )

class PositionSizer:
    """Position sizing based on risk management"""
    
    def __init__(self, max_risk_per_trade: float = 0.02):
        self.max_risk_per_trade = max_risk_per_trade  # 2% max risk per trade
    
    def calculate_position(self, symbol: str, price: float, signal_type: SignalType,
                          risk_level: RiskLevel, margin_requirement: float,
                          account_size: float = 100000) -> Dict[str, Any]:
        """Calculate optimal position size"""
        
        # Risk adjustment based on risk level
        risk_multiplier = {
            RiskLevel.LOW: 1.0,
            RiskLevel.MEDIUM: 0.7,
            RiskLevel.HIGH: 0.5,
            RiskLevel.VERY_HIGH: 0.3
        }
        
        adjusted_risk = self.max_risk_per_trade * risk_multiplier[risk_level]
        risk_amount = account_size * adjusted_risk
        
        # Stop loss distance (simplified)
        stop_distance = price * 0.01  # 1% stop loss
        
        # Calculate quantity
        quantity = int(risk_amount / stop_distance)
        
        # Calculate margin required
        notional_value = quantity * price
        margin_required = notional_value * margin_requirement
        
        return {
            'quantity': quantity,
            'notional_value': notional_value,
            'margin_required': margin_required,
            'risk_amount': risk_amount,
            'stop_distance': stop_distance
        }

class RiskManager:
    """Comprehensive risk management"""
    
    def assess_risk(self, symbol: str, data: pd.DataFrame, market_conditions: MarketConditions,
                   signal_type: SignalType) -> RiskLevel:
        """Assess overall risk level"""
        
        risk_factors = []
        
        # Volatility risk
        if 'volatility' in data.columns:
            volatility = data['volatility'].iloc[-1]
            if volatility > 0.05:
                risk_factors.append('high_volatility')
            elif volatility > 0.03:
                risk_factors.append('medium_volatility')
        
        # Market conditions risk
        if market_conditions.vix_level > 25:
            risk_factors.append('high_vix')
        
        if market_conditions.volatility > 0.04:
            risk_factors.append('market_volatility')
        
        # News sentiment risk
        if abs(market_conditions.news_sentiment) > 0.7:
            risk_factors.append('extreme_sentiment')
        
        # Time-based risk (close to market close)
        current_time = datetime.now().time()
        if current_time > time(15, 0):  # After 3 PM
            risk_factors.append('late_trading')
        
        # Determine risk level
        risk_count = len(risk_factors)
        
        if risk_count >= 3:
            return RiskLevel.VERY_HIGH
        elif risk_count == 2:
            return RiskLevel.HIGH
        elif risk_count == 1:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
