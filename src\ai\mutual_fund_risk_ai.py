"""
AI-Powered Mutual Fund Risk Calculator and Predictor
SEBI/AMFI compliant risk assessment with AI-enhanced predictions
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import joblib

from loguru import logger

class SEBIRiskLevel(Enum):
    """SEBI/AMFI standardized risk levels"""
    LOW = "Low"
    MODERATELY_LOW = "Moderately Low"
    MODERATE = "Moderate"
    MODERATELY_HIGH = "Moderately High"
    HIGH = "High"
    VERY_HIGH = "Very High"

class FundCategory(Enum):
    """Mutual fund categories as per SEBI"""
    EQUITY_LARGE_CAP = "Equity - Large Cap"
    EQUITY_MID_CAP = "Equity - Mid Cap"
    EQUITY_SMALL_CAP = "Equity - Small Cap"
    EQUITY_MULTI_CAP = "Equity - Multi Cap"
    EQUITY_FLEXI_CAP = "Equity - Flexi Cap"
    EQUITY_SECTORAL = "Equity - Sectoral/Thematic"
    DEBT_LIQUID = "Debt - Liquid"
    DEBT_ULTRA_SHORT = "Debt - Ultra Short Duration"
    DEBT_SHORT = "Debt - Short Duration"
    DEBT_MEDIUM = "Debt - Medium Duration"
    DEBT_LONG = "Debt - Long Duration"
    HYBRID_CONSERVATIVE = "Hybrid - Conservative"
    HYBRID_BALANCED = "Hybrid - Balanced"
    HYBRID_AGGRESSIVE = "Hybrid - Aggressive"

@dataclass
class FundHolding:
    """Individual fund holding"""
    security_name: str
    percentage: float
    market_cap: Optional[str] = None
    sector: Optional[str] = None
    rating: Optional[str] = None

@dataclass
class FundPortfolio:
    """Complete fund portfolio"""
    fund_name: str
    fund_code: str
    category: FundCategory
    holdings: List[FundHolding]
    equity_allocation: float
    debt_allocation: float
    cash_allocation: float
    top_10_concentration: float
    sector_concentration: Dict[str, float]
    market_cap_allocation: Dict[str, float]

@dataclass
class RiskMetrics:
    """Comprehensive risk metrics"""
    volatility: float
    beta: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    var_95: float  # Value at Risk (95%)
    cvar_95: float  # Conditional VaR (95%)
    tracking_error: Optional[float] = None
    information_ratio: Optional[float] = None

@dataclass
class SEBIRiskAssessment:
    """SEBI compliant risk assessment"""
    fund_name: str
    current_risk_level: SEBIRiskLevel
    previous_risk_level: Optional[SEBIRiskLevel]
    risk_score: float  # 0-100 scale
    risk_factors: List[str]
    volatility_score: float
    liquidity_score: float
    concentration_score: float
    credit_score: float
    duration_score: float
    assessment_date: datetime
    next_review_date: datetime

@dataclass
class AIRiskPrediction:
    """AI-powered risk prediction"""
    predicted_risk_level: SEBIRiskLevel
    confidence: float
    time_horizon: int  # days
    risk_probability_distribution: Dict[SEBIRiskLevel, float]
    key_risk_drivers: List[str]
    scenario_analysis: Dict[str, float]  # bull/base/bear scenarios

class SEBIRiskCalculator:
    """SEBI/AMFI compliant risk calculator"""
    
    def __init__(self):
        # SEBI risk level thresholds (simplified)
        self.risk_thresholds = {
            SEBIRiskLevel.LOW: (0, 15),
            SEBIRiskLevel.MODERATELY_LOW: (15, 25),
            SEBIRiskLevel.MODERATE: (25, 35),
            SEBIRiskLevel.MODERATELY_HIGH: (35, 50),
            SEBIRiskLevel.HIGH: (50, 70),
            SEBIRiskLevel.VERY_HIGH: (70, 100)
        }
        
        # Category-wise base risk scores
        self.category_base_risk = {
            FundCategory.EQUITY_LARGE_CAP: 30,
            FundCategory.EQUITY_MID_CAP: 50,
            FundCategory.EQUITY_SMALL_CAP: 70,
            FundCategory.EQUITY_MULTI_CAP: 40,
            FundCategory.EQUITY_FLEXI_CAP: 45,
            FundCategory.EQUITY_SECTORAL: 80,
            FundCategory.DEBT_LIQUID: 5,
            FundCategory.DEBT_ULTRA_SHORT: 10,
            FundCategory.DEBT_SHORT: 15,
            FundCategory.DEBT_MEDIUM: 25,
            FundCategory.DEBT_LONG: 40,
            FundCategory.HYBRID_CONSERVATIVE: 20,
            FundCategory.HYBRID_BALANCED: 35,
            FundCategory.HYBRID_AGGRESSIVE: 55
        }
    
    def calculate_risk_score(self, portfolio: FundPortfolio, risk_metrics: RiskMetrics) -> float:
        """Calculate comprehensive risk score (0-100)"""
        
        # Base risk from category
        base_risk = self.category_base_risk.get(portfolio.category, 50)
        
        # Volatility component (30% weight)
        volatility_score = min(risk_metrics.volatility * 100, 100)
        
        # Concentration risk (20% weight)
        concentration_score = self._calculate_concentration_risk(portfolio)
        
        # Liquidity risk (15% weight)
        liquidity_score = self._calculate_liquidity_risk(portfolio)
        
        # Credit risk (15% weight) - for debt funds
        credit_score = self._calculate_credit_risk(portfolio)
        
        # Market cap risk (10% weight) - for equity funds
        market_cap_score = self._calculate_market_cap_risk(portfolio)
        
        # Sector concentration risk (10% weight)
        sector_score = self._calculate_sector_risk(portfolio)
        
        # Weighted risk score
        total_score = (
            base_risk * 0.3 +
            volatility_score * 0.3 +
            concentration_score * 0.2 +
            liquidity_score * 0.15 +
            credit_score * 0.15 +
            market_cap_score * 0.1 +
            sector_score * 0.1
        )
        
        return min(max(total_score, 0), 100)
    
    def _calculate_concentration_risk(self, portfolio: FundPortfolio) -> float:
        """Calculate concentration risk score"""
        # Top 10 holdings concentration
        if portfolio.top_10_concentration > 80:
            return 90
        elif portfolio.top_10_concentration > 60:
            return 70
        elif portfolio.top_10_concentration > 40:
            return 50
        elif portfolio.top_10_concentration > 25:
            return 30
        else:
            return 10
    
    def _calculate_liquidity_risk(self, portfolio: FundPortfolio) -> float:
        """Calculate liquidity risk score"""
        if portfolio.category in [FundCategory.DEBT_LIQUID, FundCategory.DEBT_ULTRA_SHORT]:
            return 5
        elif portfolio.category in [FundCategory.EQUITY_LARGE_CAP]:
            return 15
        elif portfolio.category in [FundCategory.EQUITY_MID_CAP]:
            return 35
        elif portfolio.category in [FundCategory.EQUITY_SMALL_CAP]:
            return 70
        else:
            return 30
    
    def _calculate_credit_risk(self, portfolio: FundPortfolio) -> float:
        """Calculate credit risk score (mainly for debt funds)"""
        if portfolio.debt_allocation < 10:  # Equity funds
            return 0
        
        # Simplified credit risk based on debt allocation
        if portfolio.debt_allocation > 80:
            return 25  # Assume investment grade portfolio
        else:
            return 10
    
    def _calculate_market_cap_risk(self, portfolio: FundPortfolio) -> float:
        """Calculate market cap risk score"""
        if 'small_cap' in portfolio.market_cap_allocation:
            small_cap_allocation = portfolio.market_cap_allocation.get('small_cap', 0)
            if small_cap_allocation > 50:
                return 80
            elif small_cap_allocation > 25:
                return 50
            else:
                return 20
        return 30
    
    def _calculate_sector_risk(self, portfolio: FundPortfolio) -> float:
        """Calculate sector concentration risk"""
        if not portfolio.sector_concentration:
            return 30
        
        max_sector_allocation = max(portfolio.sector_concentration.values())
        
        if max_sector_allocation > 40:
            return 80
        elif max_sector_allocation > 25:
            return 50
        elif max_sector_allocation > 15:
            return 30
        else:
            return 10
    
    def determine_risk_level(self, risk_score: float) -> SEBIRiskLevel:
        """Determine SEBI risk level from score"""
        for level, (min_score, max_score) in self.risk_thresholds.items():
            if min_score <= risk_score < max_score:
                return level
        return SEBIRiskLevel.VERY_HIGH

class MutualFundRiskAI:
    """AI-powered mutual fund risk analyzer"""
    
    def __init__(self):
        self.sebi_calculator = SEBIRiskCalculator()
        self.risk_predictor = None
        self.scaler = StandardScaler()
        self._initialize_ai_models()
    
    def _initialize_ai_models(self):
        """Initialize AI models for risk prediction"""
        # This would be trained on historical fund data
        self.risk_predictor = RandomForestRegressor(
            n_estimators=100,
            random_state=42,
            max_depth=10
        )
    
    def analyze_fund(self, portfolio: FundPortfolio, historical_data: pd.DataFrame,
                    benchmark_data: Optional[pd.DataFrame] = None) -> Tuple[SEBIRiskAssessment, AIRiskPrediction]:
        """Comprehensive fund analysis"""
        
        # Calculate risk metrics
        risk_metrics = self._calculate_risk_metrics(historical_data, benchmark_data)
        
        # SEBI risk assessment
        sebi_assessment = self._perform_sebi_assessment(portfolio, risk_metrics)
        
        # AI risk prediction
        ai_prediction = self._generate_ai_prediction(portfolio, risk_metrics, historical_data)
        
        return sebi_assessment, ai_prediction
    
    def _calculate_risk_metrics(self, data: pd.DataFrame, benchmark: Optional[pd.DataFrame] = None) -> RiskMetrics:
        """Calculate comprehensive risk metrics"""
        
        # Returns calculation
        returns = data['nav'].pct_change().dropna()
        
        # Volatility (annualized)
        volatility = returns.std() * np.sqrt(252)
        
        # Beta (if benchmark provided)
        beta = 1.0
        tracking_error = None
        information_ratio = None
        
        if benchmark is not None:
            benchmark_returns = benchmark['value'].pct_change().dropna()
            # Align dates
            aligned_data = pd.concat([returns, benchmark_returns], axis=1, join='inner')
            if len(aligned_data) > 30:
                covariance = aligned_data.cov().iloc[0, 1]
                benchmark_variance = aligned_data.iloc[:, 1].var()
                beta = covariance / benchmark_variance if benchmark_variance != 0 else 1.0
                
                # Tracking error
                excess_returns = aligned_data.iloc[:, 0] - aligned_data.iloc[:, 1]
                tracking_error = excess_returns.std() * np.sqrt(252)
                
                # Information ratio
                if tracking_error != 0:
                    information_ratio = excess_returns.mean() * 252 / tracking_error
        
        # Sharpe ratio (assuming 6% risk-free rate)
        risk_free_rate = 0.06
        excess_return = returns.mean() * 252 - risk_free_rate
        sharpe_ratio = excess_return / volatility if volatility != 0 else 0
        
        # Sortino ratio
        downside_returns = returns[returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else volatility
        sortino_ratio = excess_return / downside_deviation if downside_deviation != 0 else 0
        
        # Maximum drawdown
        cumulative_returns = (1 + returns).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        # Value at Risk (95%)
        var_95 = abs(returns.quantile(0.05))
        
        # Conditional VaR (95%)
        cvar_95 = abs(returns[returns <= returns.quantile(0.05)].mean())
        
        return RiskMetrics(
            volatility=volatility,
            beta=beta,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            var_95=var_95,
            cvar_95=cvar_95,
            tracking_error=tracking_error,
            information_ratio=information_ratio
        )
    
    def _perform_sebi_assessment(self, portfolio: FundPortfolio, risk_metrics: RiskMetrics) -> SEBIRiskAssessment:
        """Perform SEBI compliant risk assessment"""
        
        # Calculate risk score
        risk_score = self.sebi_calculator.calculate_risk_score(portfolio, risk_metrics)
        
        # Determine risk level
        current_risk_level = self.sebi_calculator.determine_risk_level(risk_score)
        
        # Identify risk factors
        risk_factors = self._identify_risk_factors(portfolio, risk_metrics, risk_score)
        
        # Component scores
        volatility_score = min(risk_metrics.volatility * 100, 100)
        liquidity_score = self.sebi_calculator._calculate_liquidity_risk(portfolio)
        concentration_score = self.sebi_calculator._calculate_concentration_risk(portfolio)
        credit_score = self.sebi_calculator._calculate_credit_risk(portfolio)
        duration_score = 30  # Simplified duration risk
        
        return SEBIRiskAssessment(
            fund_name=portfolio.fund_name,
            current_risk_level=current_risk_level,
            previous_risk_level=None,  # Would be fetched from database
            risk_score=risk_score,
            risk_factors=risk_factors,
            volatility_score=volatility_score,
            liquidity_score=liquidity_score,
            concentration_score=concentration_score,
            credit_score=credit_score,
            duration_score=duration_score,
            assessment_date=datetime.now(),
            next_review_date=datetime.now() + timedelta(days=30)
        )
    
    def _identify_risk_factors(self, portfolio: FundPortfolio, risk_metrics: RiskMetrics, risk_score: float) -> List[str]:
        """Identify key risk factors"""
        factors = []
        
        # High volatility
        if risk_metrics.volatility > 0.25:
            factors.append("High volatility (>25% annualized)")
        
        # High concentration
        if portfolio.top_10_concentration > 60:
            factors.append("High concentration in top 10 holdings")
        
        # Small cap exposure
        if 'small_cap' in portfolio.market_cap_allocation:
            if portfolio.market_cap_allocation['small_cap'] > 30:
                factors.append("Significant small cap exposure")
        
        # Sector concentration
        if portfolio.sector_concentration:
            max_sector = max(portfolio.sector_concentration.values())
            if max_sector > 30:
                factors.append("High sector concentration")
        
        # High beta
        if risk_metrics.beta > 1.3:
            factors.append("High market sensitivity (Beta > 1.3)")
        
        # Large drawdowns
        if risk_metrics.max_drawdown > 0.3:
            factors.append("History of large drawdowns")
        
        return factors
    
    def _generate_ai_prediction(self, portfolio: FundPortfolio, risk_metrics: RiskMetrics,
                               historical_data: pd.DataFrame) -> AIRiskPrediction:
        """Generate AI-powered risk prediction"""
        
        # Feature engineering for prediction
        features = self._extract_features(portfolio, risk_metrics, historical_data)
        
        # Mock prediction (in real implementation, use trained model)
        current_risk_score = self.sebi_calculator.calculate_risk_score(portfolio, risk_metrics)
        
        # Simulate risk level changes
        risk_change_probability = np.random.uniform(-0.1, 0.1)
        predicted_score = max(0, min(100, current_risk_score + risk_change_probability * 10))
        
        predicted_risk_level = self.sebi_calculator.determine_risk_level(predicted_score)
        
        # Confidence based on data quality and model performance
        confidence = np.random.uniform(0.7, 0.9)
        
        # Risk probability distribution
        risk_distribution = self._calculate_risk_distribution(predicted_score)
        
        # Key risk drivers
        risk_drivers = [
            "Market volatility trends",
            "Portfolio concentration changes",
            "Sector rotation patterns",
            "Interest rate environment"
        ]
        
        # Scenario analysis
        scenario_analysis = {
            'bull_market': predicted_score * 0.8,
            'base_case': predicted_score,
            'bear_market': predicted_score * 1.3
        }
        
        return AIRiskPrediction(
            predicted_risk_level=predicted_risk_level,
            confidence=confidence,
            time_horizon=30,
            risk_probability_distribution=risk_distribution,
            key_risk_drivers=risk_drivers,
            scenario_analysis=scenario_analysis
        )
    
    def _extract_features(self, portfolio: FundPortfolio, risk_metrics: RiskMetrics,
                         historical_data: pd.DataFrame) -> np.ndarray:
        """Extract features for ML prediction"""
        
        features = [
            portfolio.equity_allocation,
            portfolio.debt_allocation,
            portfolio.top_10_concentration,
            risk_metrics.volatility,
            risk_metrics.beta,
            risk_metrics.max_drawdown,
            len(historical_data),  # Data history length
            historical_data['nav'].pct_change().skew(),  # Return skewness
            historical_data['nav'].pct_change().kurtosis()  # Return kurtosis
        ]
        
        # Add sector concentration features
        if portfolio.sector_concentration:
            features.extend([
                max(portfolio.sector_concentration.values()),
                len(portfolio.sector_concentration),
                np.std(list(portfolio.sector_concentration.values()))
            ])
        else:
            features.extend([0, 0, 0])
        
        return np.array(features).reshape(1, -1)
    
    def _calculate_risk_distribution(self, predicted_score: float) -> Dict[SEBIRiskLevel, float]:
        """Calculate probability distribution across risk levels"""
        
        # Create normal distribution around predicted score
        std_dev = 5  # Standard deviation for uncertainty
        
        probabilities = {}
        for level in SEBIRiskLevel:
            min_score, max_score = self.sebi_calculator.risk_thresholds[level]
            
            # Calculate probability that score falls in this range
            from scipy import stats
            prob = stats.norm.cdf(max_score, predicted_score, std_dev) - \
                   stats.norm.cdf(min_score, predicted_score, std_dev)
            
            probabilities[level] = max(0, prob)
        
        # Normalize probabilities
        total_prob = sum(probabilities.values())
        if total_prob > 0:
            probabilities = {k: v/total_prob for k, v in probabilities.items()}
        
        return probabilities
    
    def generate_risk_report(self, portfolio: FundPortfolio, sebi_assessment: SEBIRiskAssessment,
                           ai_prediction: AIRiskPrediction) -> Dict[str, Any]:
        """Generate comprehensive risk report"""
        
        return {
            'fund_details': {
                'name': portfolio.fund_name,
                'category': portfolio.category.value,
                'equity_allocation': portfolio.equity_allocation,
                'debt_allocation': portfolio.debt_allocation
            },
            'sebi_assessment': {
                'current_risk_level': sebi_assessment.current_risk_level.value,
                'risk_score': sebi_assessment.risk_score,
                'risk_factors': sebi_assessment.risk_factors,
                'component_scores': {
                    'volatility': sebi_assessment.volatility_score,
                    'liquidity': sebi_assessment.liquidity_score,
                    'concentration': sebi_assessment.concentration_score,
                    'credit': sebi_assessment.credit_score
                }
            },
            'ai_prediction': {
                'predicted_risk_level': ai_prediction.predicted_risk_level.value,
                'confidence': ai_prediction.confidence,
                'time_horizon': ai_prediction.time_horizon,
                'key_drivers': ai_prediction.key_risk_drivers,
                'scenarios': ai_prediction.scenario_analysis
            },
            'recommendations': self._generate_recommendations(sebi_assessment, ai_prediction)
        }
    
    def _generate_recommendations(self, sebi_assessment: SEBIRiskAssessment,
                                ai_prediction: AIRiskPrediction) -> List[str]:
        """Generate investment recommendations"""
        
        recommendations = []
        
        # Risk level based recommendations
        if sebi_assessment.current_risk_level in [SEBIRiskLevel.HIGH, SEBIRiskLevel.VERY_HIGH]:
            recommendations.append("Consider reducing allocation due to high risk level")
            recommendations.append("Monitor fund performance closely")
        
        # Concentration risk
        if sebi_assessment.concentration_score > 60:
            recommendations.append("High concentration risk - consider diversified alternatives")
        
        # Volatility risk
        if sebi_assessment.volatility_score > 50:
            recommendations.append("High volatility - suitable only for risk-tolerant investors")
        
        # AI prediction based
        if ai_prediction.predicted_risk_level.value != sebi_assessment.current_risk_level.value:
            recommendations.append(f"AI predicts risk level change to {ai_prediction.predicted_risk_level.value}")
        
        # Scenario analysis
        bear_case_risk = ai_prediction.scenario_analysis.get('bear_market', 0)
        if bear_case_risk > 70:
            recommendations.append("High risk in adverse market conditions")
        
        return recommendations
